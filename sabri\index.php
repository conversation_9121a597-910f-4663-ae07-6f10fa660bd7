<?php
	session_start();
	$nonavbar='';//متغیرعرفنا یستخدم لعدم وضع القائمة في صفحة الدخول تم برمجتو في ملف init.php
	$PageTitle = 'Logic';//متغير من التابع تم تعريفه ليقوم برضع اسم الصفحة تم برمجتوه في ملف function.php
	if (isset($_COOKIE['username'])) {//اذا المستخدم مسجل من قبل لا ينتقل مباشر الى الصفحة الرئيسية
		header('Location: dashboard.php'); //register to dashboard page
	}
    include 'init.php';
   

   //check if user coming from http post request
   //التاکید انا المستخدم فایت عن طريق request
	if($_SERVER['REQUEST_METHOD'] == 'POST'){
		
		$username = $_POST['user'];
		$password = $_POST['pass'];
		$hashedpass = sha1($password);
		
		//check if the user exist in database
		$stmt = $con->prepare("SELECT 
									userID,username, password 
								FRoM 
									users 
								WHERE 
									username = ? 
								AND 
									password = ? 
								AND 
									groupID = 1
									LIMIT 1");
		$stmt->execute(array($username, $hashedpass));
		$row = $stmt->fetch();
		$count =$stmt->rowCount();
		
        //if count > 0 this mean the datebase contain about this username
        if($count>0){//اذا كان المدخلات صحيحة ينتقل الى الصفحة الرئيسية
        	/*
        	$_SESSION['username']=$username; //register session name
        	$_SESSION['ID']=$row['userID']; //register session ID
        	*/
        	setcookie('user', $row['userID'] ,time()+36000, '/',null,null,true);
			setcookie('username', $row['userID'] ,time()+36000, '/',null,null,true);
        	header('Location: dashboard.php'); //register to dashboard page
        //	exit();

        	//echo 'welcom'.' '.$username;
        } 

	}
?>
<!--قائمة تسجيل الدخول-->
<form class="login" action="<?php echo $_SERVER['PHP_SELF'] ?>" method="POST">
    <h4 class="text-center">Admin login</h4>
    <input class="form-control input-lg"  type="text" name="user" placeholder="Username" autocomplete="off"/>
    <input class="form-control input-lg"  type="password" name="pass" placeholder="Password" autocomplete="new-password"/>
    <input class="btn btn-lg btn-primary btn-block"  type="submit" value="Logic"/>
</form>


<?php
    include $tp1 . 'footer.php';
?>
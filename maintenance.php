<?php
	ob_start();
	session_start();

	$nonavbar='';
	$pageTitle = 'Show Maintenance';


	include 'init.php';

	// Check If Get Request item Is Numeric & Get Its Integer Value
	$itemid = isset($_GET['acid']) && is_numeric($_GET['acid']) ? intval($_GET['acid']) : 0;

	if (!isset($_COOKIE['username'])) {
	// Select All Data Depend On This ID
	$stmt = $con->prepare("SELECT 
										maintenance.*, 
										ac.Code AS category_name
									FROM 
										maintenance
									INNER JOIN 
										ac 
									ON 
										ac.Id = maintenance.ACID 
									WHERE
										ACID = ?
									ORDER BY 
										Id DESC");

			// Execute The Statement

			$stmt->execute(array($itemid));

			// Assign To Variable 
			$items = $stmt->fetchAll();

	if (! empty($items)) {
?>
		

		<header>
        		<img src="header.jpg" alt="Image 1">
		</header>

    
		<h1 class="text-center"><?php echo  $items[0]['category_name']; ?></h1>
		<div class="container">
			<div class="table-responsive">
				<table class="main-table text-center table table-bordered">
					<tr class="success">
						<td>Date Now</td>
						<td>Date Next</td>
						<td>Reason</td>
					</tr>
					<?php
						foreach($items as $item) {
							echo "<tr>";
								echo "<td>" . $item['DateNow'] ."</td>";
								echo "<td>" . $item['DateNext'] . "</td>";
								echo "<td>" . $item['Reason'] ."</td>";
							echo "</tr>";
						}
					?>
					<tr>
				</table>
			</div>
		</div>
<?php
	} else {
		echo '<div class="container">';
			echo '<div class="alert alert-danger">There\'s no Mauntenace for This AC yet</div>';
		echo '</div>';
	}
	include $tp1 . 'footer.php';
	ob_end_flush();
}else {
		header("Location: sabri/maintenance.php?do=Add&&acid=". $itemid);
		exit;
	}
	
?>
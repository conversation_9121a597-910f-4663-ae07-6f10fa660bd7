$(function(){


    'use strict';

    // Switch Between Login & Signup

    $('.login-page h1 span').click(function () {

        $(this).addClass('selected').siblings().removeClass('selected');

        $('.login-page form').hide();

        $('.' + $(this).data('class')).fadeIn(100);

    });

    // Add smooth scrolling to all links
    $('a[href*="#"]').on('click', function(e) {
        e.preventDefault();

        $('html, body').animate({
            scrollTop: $($(this).attr('href')).offset().top - 70
        }, 500, 'linear');
    });

    // Add loading animation to buttons
    $('.btn').on('click', function() {
        var $btn = $(this);
        if (!$btn.hasClass('no-loading')) {
            $btn.addClass('loading');
            setTimeout(function() {
                $btn.removeClass('loading');
            }, 2000);
        }
    });

    // Add hover effects to cards
    $('.card, .item-box, .panel').hover(
        function() {
            $(this).addClass('hover-effect');
        },
        function() {
            $(this).removeClass('hover-effect');
        }
    );

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Add fade-in animation to elements
    $('.fade-in').each(function(i) {
        $(this).delay(i * 100).animate({
            opacity: 1,
            top: 0
        }, 500);
    });

      
    // Trigger The Selectboxit

    $("select").selectBoxIt({

        autoWidth: false

    });

    
    //hide placeholder on form focus
    //اخفاء الكتابة الموجودة في المربع التنصيصي وقت النقر علي 
    $('[placeholder]').focus(function(){
        
        $(this).attr('data-text',$(this).attr('placeholder'));
        $(this).attr('placeholder', '');
    }).blur(function () {//لترجیع الکتابئ بعد شیل النقر
        $(this).attr('placeholder',$(this).attr('data-text'));
		
	});

    //add asterisk(*) on required field

    $('input').each(function(){
        if ($(this).attr('required') == 'required') {
            $(this).after('<span class="asterisk">*</span>');
        }
    });


    //convert password field to text field on hover
    var passfield = $('.password');

    $('.show-pass').hover(function() {

        passfield.attr('type', 'text');

    },function(){
        
        passfield.attr('type','password');
    });

    //confirmation massage on button
    $('.confirm').click(function(){

        return confirm('Are you sure?');
    });

    //ابع کتابئ محتود الحقل فی  معلومات الایتم

    $('.live').keyup(function () {

        $($(this).data('class')).text($(this).val());

    });

    $("#query").autocomplete({
        source : 'search.php',
        select : function(event,ui){
            $("#query").html(ui.item.value);
        }
    });

});

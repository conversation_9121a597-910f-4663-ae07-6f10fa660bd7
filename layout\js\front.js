$(function(){


    'use strict';

    // Switch Between Login & Signup

    $('.login-page h1 span').click(function () {

        $(this).addClass('selected').siblings().removeClass('selected');

        $('.login-page form').hide();

        $('.' + $(this).data('class')).fadeIn(100);

    });

    // Add smooth scrolling to all links
    $('a[href*="#"]').on('click', function(e) {
        e.preventDefault();

        $('html, body').animate({
            scrollTop: $($(this).attr('href')).offset().top - 70
        }, 500, 'linear');
    });

    // Add loading animation to buttons
    $('.btn').on('click', function() {
        var $btn = $(this);
        if (!$btn.hasClass('no-loading')) {
            $btn.addClass('loading');
            setTimeout(function() {
                $btn.removeClass('loading');
            }, 2000);
        }
    });

    // Add hover effects to cards
    $('.card, .item-box, .panel').hover(
        function() {
            $(this).addClass('hover-effect');
        },
        function() {
            $(this).removeClass('hover-effect');
        }
    );

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Add fade-in animation to elements
    $('.fade-in').each(function(i) {
        $(this).delay(i * 100).animate({
            opacity: 1,
            top: 0
        }, 500);
    });

      
    // Trigger The Selectboxit

    $("select").selectBoxIt({

        autoWidth: false

    });

    
    //hide placeholder on form focus
    //اخفاء الكتابة الموجودة في المربع التنصيصي وقت النقر علي 
    $('[placeholder]').focus(function(){
        
        $(this).attr('data-text',$(this).attr('placeholder'));
        $(this).attr('placeholder', '');
    }).blur(function () {//لترجیع الکتابئ بعد شیل النقر
        $(this).attr('placeholder',$(this).attr('data-text'));
		
	});

    //add asterisk(*) on required field

    $('input').each(function(){
        if ($(this).attr('required') == 'required') {
            $(this).after('<span class="asterisk">*</span>');
        }
    });


    //convert password field to text field on hover
    var passfield = $('.password');

    $('.show-pass').hover(function() {

        passfield.attr('type', 'text');

    },function(){
        
        passfield.attr('type','password');
    });

    //confirmation massage on button
    $('.confirm').click(function(){

        return confirm('Are you sure?');
    });

    //ابع کتابئ محتود الحقل فی  معلومات الایتم

    $('.live').keyup(function () {

        $($(this).data('class')).text($(this).val());

    });

    $("#query").autocomplete({
        source : 'search.php',
        select : function(event,ui){
            $("#query").html(ui.item.value);
        }
    });

});

// Professional Homepage Interactions and Animations
$(document).ready(function() {

    // Counter animation for statistics
    function animateCounters() {
        $('.stat-number').each(function() {
            var $this = $(this);
            var countTo = $this.attr('data-count');

            if (countTo) {
                $({ countNum: 0 }).animate({
                    countNum: countTo
                }, {
                    duration: 2000,
                    easing: 'swing',
                    step: function() {
                        $this.text(Math.floor(this.countNum));
                    },
                    complete: function() {
                        $this.text(countTo);
                    }
                });
            }
        });
    }

    // Trigger counter animation when stats section is visible
    function checkCounters() {
        var statsSection = $('.about-stats');
        if (statsSection.length && !statsSection.hasClass('animated')) {
            var statsTop = statsSection.offset().top;
            var statsBottom = statsTop + statsSection.outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();

            if (statsBottom > viewportTop && statsTop < viewportBottom) {
                statsSection.addClass('animated');
                animateCounters();
            }
        }
    }

    // Enhanced smooth scrolling
    $('.smooth-scroll').on('click', function(e) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: target.offset().top - 70
            }, 1000);
        }
    });

    // RTL Support for animations
    function isRTL() {
        return $('body').css('direction') === 'rtl';
    }

    // Adjust hover effects for RTL
    $('.feature-item').hover(
        function() {
            if (isRTL()) {
                $(this).css('transform', 'translateX(10px)');
            } else {
                $(this).css('transform', 'translateX(-10px)');
            }
        },
        function() {
            $(this).css('transform', 'translateX(0)');
        }
    );

    $('.contact-item').hover(
        function() {
            if (isRTL()) {
                $(this).css('transform', 'translateX(10px)');
            } else {
                $(this).css('transform', 'translateX(-10px)');
            }
        },
        function() {
            $(this).css('transform', 'translateX(0)');
        }
    );

    // Contact form handling
    $('#contactForm').on('submit', function(e) {
        e.preventDefault();

        var $form = $(this);
        var $submitBtn = $form.find('button[type="submit"]');
        var originalText = $submitBtn.html();

        // Show loading state
        $submitBtn.html('<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...');
        $submitBtn.prop('disabled', true);

        // Simulate form submission
        setTimeout(function() {
            // Show success message
            var successMsg = '<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                           'تم إرسال رسالتكم بنجاح! سنتواصل معكم قريباً.' +
                           '<button type="button" class="close" data-dismiss="alert">' +
                           '<span>&times;</span></button></div>';

            $form.before(successMsg);

            // Reset form
            $form[0].reset();

            // Reset button
            $submitBtn.html(originalText);
            $submitBtn.prop('disabled', false);

            // Auto-hide success message
            setTimeout(function() {
                $('.alert-success').fadeOut();
            }, 5000);
        }, 2000);
    });

    // Floating animation for hero elements
    setInterval(function() {
        $('.floating-card').toggleClass('float-animation');
    }, 3000);

    // Service card interactions
    $('.service-card').hover(
        function() {
            $(this).find('.service-icon').addClass('icon-bounce');
        },
        function() {
            $(this).find('.service-icon').removeClass('icon-bounce');
        }
    );

    // Portfolio hover effects
    $('.portfolio-item').hover(
        function() {
            $(this).addClass('portfolio-hover');
        },
        function() {
            $(this).removeClass('portfolio-hover');
        }
    );

    // Scroll effects
    $(window).scroll(function() {
        checkCounters();

        var scrolled = $(window).scrollTop();

        // Parallax effect for hero
        if ($('.hero-section').length) {
            var speed = 0.5;
            var yPos = -(scrolled * speed);
            $('.hero-section').css('transform', 'translateY(' + yPos + 'px)');
        }

        // Hide/show scroll indicator
        if (scrolled > 100) {
            $('.scroll-indicator').fadeOut();
        } else {
            $('.scroll-indicator').fadeIn();
        }

        // Navbar background on scroll
        if (scrolled > 50) {
            $('.navbar').addClass('navbar-scrolled');
        } else {
            $('.navbar').removeClass('navbar-scrolled');
        }
    });

    // Add dynamic CSS for animations
    $('<style>').prop('type', 'text/css').html(`
        .float-animation {
            transform: translateY(-10px);
            transition: transform 0.3s ease;
        }
        .icon-bounce {
            animation: iconBounce 0.6s ease;
        }
        .portfolio-hover img {
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }
        .navbar-scrolled {
            background: rgba(255,255,255,0.95) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        @keyframes iconBounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        .alert {
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: right;
        }
        .btn:hover {
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }
        /* RTL specific animations */
        .rtl .feature-item:hover {
            transform: translateX(10px) !important;
        }
        .rtl .contact-item:hover {
            transform: translateX(10px) !important;
        }
        /* Ensure proper RTL text alignment */
        .rtl * {
            text-align: right;
        }
        .rtl .text-center {
            text-align: center !important;
        }
        .rtl .hero-buttons,
        .rtl .cta-buttons {
            text-align: right;
        }
        @media (max-width: 768px) {
            .rtl .hero-buttons,
            .rtl .cta-buttons {
                text-align: center;
            }
        }
    `).appendTo('head');

    // Add RTL class to body
    $('body').addClass('rtl');

    // Initialize counters check
    checkCounters();

    // Add loading overlay functionality
    function showLoading() {
        $('body').append('<div class="loading-overlay"><div class="loading-spinner"></div></div>');
    }

    function hideLoading() {
        $('.loading-overlay').fadeOut(function() {
            $(this).remove();
        });
    }

    // Expose loading functions globally
    window.showLoading = showLoading;
    window.hideLoading = hideLoading;
});

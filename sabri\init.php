<?php

include 'connect.php';
//Routes
 $tp1= 'includes/templates/';    //Templates directory
 $func ='includes/functions/';   //functions directory  
 $css='layout/css/';             //Css directory
 $js='layout/js/';               //js directory
 

//include the important files
 	include $func . 'function.php';
 	include $tp1 . 'header.php';
 //استدعاء القائمة العليا في جميع الملفات ما عدا الصفحات التي يكتب فيها متغير $nonavbar
 //	include navbar on all pages expect the one with $nonavbar vairable
 	if (!isset($nonavbar)) { include $tp1 . 'navbar.php'; }  
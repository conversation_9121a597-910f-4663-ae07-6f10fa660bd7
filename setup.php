<?php
/**
 * ملف الإعداد السريع
 * يساعد في إنشاء الملفات والمجلدات المطلوبة
 */

// منع الوصول المباشر إلا من localhost
if (!in_array($_SERVER['REMOTE_ADDR'], ['127.0.0.1', '::1', 'localhost'])) {
    die('Access denied');
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد النظام - UNHCR AC System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #333; }
        .btn-danger { background: #dc3545; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إعداد نظام إدارة صيانة المكيفات</h1>
            <p>مرحباً بك في معالج الإعداد السريع</p>
        </div>

        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $_POST['action'] ?? '';
            
            switch ($action) {
                case 'create_folders':
                    echo '<div class="step">';
                    echo '<h3>إنشاء المجلدات المطلوبة</h3>';
                    
                    $folders = [
                        'uploads',
                        'logs',
                        'cache',
                        'backups',
                        'assets/images',
                        'assets/uploads'
                    ];
                    
                    foreach ($folders as $folder) {
                        if (!is_dir($folder)) {
                            if (mkdir($folder, 0755, true)) {
                                echo '<p class="success">✓ تم إنشاء مجلد: ' . $folder . '</p>';
                            } else {
                                echo '<p class="error">✗ فشل إنشاء مجلد: ' . $folder . '</p>';
                            }
                        } else {
                            echo '<p class="warning">⚠ المجلد موجود بالفعل: ' . $folder . '</p>';
                        }
                    }
                    echo '</div>';
                    break;
                    
                case 'check_permissions':
                    echo '<div class="step">';
                    echo '<h3>فحص صلاحيات الملفات</h3>';
                    
                    $checkPaths = [
                        'layout/css',
                        'layout/js',
                        'includes/templates',
                        'uploads',
                        'logs'
                    ];
                    
                    foreach ($checkPaths as $path) {
                        if (is_dir($path)) {
                            $perms = fileperms($path);
                            $readable = is_readable($path);
                            $writable = is_writable($path);
                            
                            echo '<p>';
                            echo '<strong>' . $path . '</strong>: ';
                            echo 'الصلاحيات: ' . substr(sprintf('%o', $perms), -4) . ' | ';
                            echo ($readable ? '<span class="success">قابل للقراءة</span>' : '<span class="error">غير قابل للقراءة</span>') . ' | ';
                            echo ($writable ? '<span class="success">قابل للكتابة</span>' : '<span class="error">غير قابل للكتابة</span>');
                            echo '</p>';
                        } else {
                            echo '<p class="error">المسار غير موجود: ' . $path . '</p>';
                        }
                    }
                    echo '</div>';
                    break;
                    
                case 'create_htaccess':
                    echo '<div class="step">';
                    echo '<h3>إنشاء ملف .htaccess</h3>';
                    
                    $htaccessContent = '# UNHCR AC System - Security Settings
RewriteEngine On

# Prevent access to sensitive files
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
</IfModule>';

                    if (file_put_contents('.htaccess', $htaccessContent)) {
                        echo '<p class="success">✓ تم إنشاء ملف .htaccess بنجاح</p>';
                    } else {
                        echo '<p class="error">✗ فشل إنشاء ملف .htaccess</p>';
                    }
                    echo '</div>';
                    break;
                    
                case 'test_database':
                    echo '<div class="step">';
                    echo '<h3>اختبار الاتصال بقاعدة البيانات</h3>';
                    
                    try {
                        include 'sabri/connect.php';
                        if (isset($con) && $con instanceof PDO) {
                            echo '<p class="success">✓ الاتصال بقاعدة البيانات ناجح</p>';
                            
                            // اختبار استعلام بسيط
                            $stmt = $con->query('SELECT VERSION() as version');
                            $result = $stmt->fetch();
                            echo '<p>إصدار MySQL: ' . $result['version'] . '</p>';
                        } else {
                            echo '<p class="error">✗ فشل الاتصال بقاعدة البيانات</p>';
                        }
                    } catch (Exception $e) {
                        echo '<p class="error">✗ خطأ في قاعدة البيانات: ' . $e->getMessage() . '</p>';
                    }
                    echo '</div>';
                    break;
            }
        }
        ?>

        <div class="step">
            <h3>خطوات الإعداد</h3>
            <p>اتبع هذه الخطوات لإعداد النظام بشكل صحيح:</p>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="create_folders">
                <button type="submit" class="btn">1. إنشاء المجلدات المطلوبة</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="check_permissions">
                <button type="submit" class="btn btn-warning">2. فحص صلاحيات الملفات</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="create_htaccess">
                <button type="submit" class="btn btn-success">3. إنشاء ملف .htaccess</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="test_database">
                <button type="submit" class="btn btn-danger">4. اختبار قاعدة البيانات</button>
            </form>
        </div>

        <div class="step">
            <h3>معلومات مهمة</h3>
            <ul>
                <li><strong>مجلد المشروع:</strong> <?php echo __DIR__; ?></li>
                <li><strong>عنوان الموقع:</strong> <a href="http://localhost:8000" target="_blank">http://localhost:8000</a></li>
                <li><strong>صفحة التشخيص:</strong> <a href="diagnostic.php" target="_blank">diagnostic.php</a></li>
                <li><strong>دليل المستخدم:</strong> <a href="USER_GUIDE.md" target="_blank">USER_GUIDE.md</a></li>
            </ul>
        </div>

        <div class="step">
            <h3>إعدادات قاعدة البيانات</h3>
            <p>تأكد من إعداد قاعدة البيانات في ملف <code>.env</code>:</p>
            <pre>DB_HOST=localhost
DB_NAME=sabricom
DB_USER=root
DB_PASS=</pre>
        </div>

        <div class="step">
            <h3>الخطوات التالية</h3>
            <ol>
                <li>تأكد من تشغيل خادم MySQL</li>
                <li>أنشئ قاعدة البيانات إذا لم تكن موجودة</li>
                <li>استورد هيكل قاعدة البيانات</li>
                <li>اذهب إلى <a href="index.php" class="btn">الصفحة الرئيسية</a></li>
            </ol>
        </div>

        <div class="step">
            <h3>روابط مفيدة</h3>
            <a href="index.php" class="btn">الصفحة الرئيسية</a>
            <a href="login.php" class="btn btn-success">تسجيل الدخول</a>
            <a href="dashboard.php" class="btn btn-warning">لوحة التحكم</a>
            <a href="diagnostic.php" class="btn btn-danger">صفحة التشخيص</a>
        </div>
    </div>
</body>
</html>

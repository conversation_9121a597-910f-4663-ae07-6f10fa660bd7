<?php

	//Error Reporting
	
	ini_set('display_errors', 'On');
	error_reporting(E_ALL);

	include 'sabri/connect.php';
	include 'includes/functions/Classes/PHPExcel.php';
	include 'includes/functions/Classes/PHPExcel/IOFactory.php';
	require_once 'vendor/autoload.php';
	$sessionUser = '';
	
	if (isset($_COOKIE['user'])) {
		$sessionUser = $_COOKIE['user'];
	}

	//Routes
	 $tp1= 'includes/templates/';    //Templates directory
	 $func ='includes/functions/';   //functions directory  
	 $css='layout/css/';             //Css directory
	 $js='layout/js/';               //js directory
	 

	//include the important files
	 	include $func . 'function.php';
	 	include $tp1 . 'header.php';
	 	if (!isset($nonavbar)) { include $tp1 . 'navbar.php'; }  

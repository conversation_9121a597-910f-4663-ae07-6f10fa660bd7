<?php

	/*
	================================================
	== Category Page
	================================================
	*/

	ob_start(); // Output Buffering Start

	session_start();

	$pageTitle = 'Categories';

	if (isset($_COOKIE['username'])) {

		include 'init.php';

		$do = isset($_GET['do']) ? $_GET['do'] : 'Manage';

		if ($do == 'Manage') {
			
			$sort = 'asc';

			$sort_array = array('asc', 'desc');

			if (isset($_GET['sort']) && in_array($_GET['sort'], $sort_array)) {

				$sort = $_GET['sort'];

			}

			$stmt = $con->prepare("SELECT 
										categories.*, 
										company.name AS company_name
									FROM 
									categories
									INNER JOIN 
									company 
									ON 
									company.Id = categories.CompanyID 
									ORDER BY 
										Id DESC");

			// Execute The Statement

			$stmt->execute();

			// Assign To Variable 

			$cats = $stmt->fetchAll();
			?>

			<h1 class="text-center">Manage Categories</h1>
			<div class="container categories">
				<div class="panel panel-default">
					<div class="panel-heading"><i class="fa fa-edit"></i> Manage Categories
						<div class="option pull-right">
							<i class="fa fa-sort"></i> Ordering: [
							<a class="<?php if ($sort == 'asc') { echo 'active'; } ?>" href="?sort=asc">Asc</a> | 
							<a class="<?php if ($sort == 'desc') { echo 'active'; } ?>" href="?sort=desc">Desc</a> ]
							<i class="fa fa-eye"></i> View: [
							<span class="active" data-view="full">Full</span> |
							<span data-view="classic">Classic</span> ]
						</div>
					<div class="panel-body">
						<?php
							foreach($cats as $cat) {
								echo "<div class='cat'>";
									echo "<div class='hidden-buttons'>";
										echo "<a href='categories.php?do=Edit&catid=" . $cat['Id'] . "' class='btn btn-xs btn-primary'><i class='fa fa-edit'></i> Edit</a>";
										echo "<a href='categories.php?do=Delete&catid=" . $cat['Id'] . "' class='confirm btn btn-xs btn-danger'><i class='fa fa-close'></i> Delete</a>";
									echo "</div>";
									echo "<h3>" . $cat['Name'] . '</h3>';
									echo "<div class='full-view'>";
										echo "<p>"; if($cat['company_name'] == '') { echo 'This category has no company'; } else { echo $cat['company_name']; } echo "</p>";
										echo "<p>"; if($cat['Notes'] == '') { echo 'This category has no Notes'; } else { echo $cat['Notes']; } echo "</p>";
									echo "</div>";
								echo "</div>"; 
								echo "<hr>";

							}
						?>
					</div>
				</div>
				<div class="col-sm-offset-5 col-sm-10">
							<a class="add-category btn btn-primary" href="categories.php?do=Add"><i class="fa fa-plus"></i> Add New Category</a>
				</div>
			</div>



<?php		} elseif ($do == 'Add') {?>
			<h1 class="text-center">Add New Category</h1>
			<div class="container">
				<form class="form-horizontal" action="?do=Insert" method="POST">
					<!-- Start Name Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Name</label>
						<div class="col-sm-10 col-md-6">
							<input type="text" name="Name" class="form-control" autocomplete="off" required="required" placeholder="Name Of The Category" />
						</div>
					</div>
					<!-- End Name Field -->
					<!-- Start Company Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Company</label>
						<div class="col-sm-10 col-md-6">
							<select name="company">
								<option value="0">...</option>
								<?php
									$allCats = getAllFrom("*", "company", "", "", "Id","ASC");
									foreach ($allCats as $c) {
										echo "<option value='" . $c['Id'] . "'>" . $c['Name'] . "</option>";
									}	
								?>
							</select>
						</div>
					</div>
					<!-- End Company Field -->
					<!-- Start Notes Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Notes</label>
						<div class="col-sm-10 col-md-6">
							<input type="text" name="Notes" class="form-control" placeholder="Notes The Category" />
						</div>
					</div>
					<!-- End Notes Field -->
					<!-- Start Submit Field -->
					<div class="form-group form-group-lg">
						<div class="col-sm-offset-5 col-sm-10">
							<input type="submit" value="Add Category" class="btn btn-primary btn-lg" />
						</div>
					</div>
					<!-- End Submit Field -->	
				</form>
			</div>





<?php	} elseif ($do == 'Insert') {

			if ($_SERVER['REQUEST_METHOD'] == 'POST') {

				echo "<h1 class='text-center'>Insert Category</h1>";
				echo "<div class='container'>";

				// Get Variables From The Form

				$name 		= $_POST['Name'];
				$company 	= $_POST['company'];
				$Notes 		= $_POST['Notes'];
				

				// Check If Category Exist in Database

				$check = checkitem("Name", "categories", $name);

				if ($check == 1) {

					$themsg = '<div class="alert alert-danger">Sorry This Category Is Exist</div>';

					redirectHome($themsg, 'back');

				} else {

					// Insert Category Info In Database

					$stmt = $con->prepare("INSERT INTO 

						categories(name, CompanyID, Notes)

						VALUES(:zname, :zcom, :znotes)");

					$stmt->execute(array(
						'zname' 	=> $name,
						'zcom'  	=> $company,
						'znotes' 	=> $Notes,
					));

					// Echo Success Message

					$themsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Inserted</div>';

					redirectHome($themsg, 'back');

				}

			} else {

				echo "<div class='container'>";

				$themsg = '<div class="alert alert-danger">Sorry You Cant Browse This Page Directly</div>';

				redirectHome($themsg, 'back');

					echo "</div>";

			}

			echo "</div>";



		} elseif ($do == 'Edit') {
			// Check If Get Request catid Is Numeric & Get Its Integer Value

			$catid = isset($_GET['catid']) && is_numeric($_GET['catid']) ? intval($_GET['catid']) : 0;

			// Select All Data Depend On This ID

			$stmt = $con->prepare("SELECT * FROM categories WHERE Id = ?");

			// Execute Query

			$stmt->execute(array($catid));

			// Fetch The Data

			$cat = $stmt->fetch();

			// The Row Count

			$count = $stmt->rowCount();

			// If There's Such ID Show The Form

			if ($count > 0) { ?>

				<h1 class="text-center">Edit Category</h1>
				<div class="container">
					<form class="form-horizontal" action="?do=Update" method="POST">
						<input type="hidden" name="catid" value="<?php echo $catid ?>" />
						<!-- Start Name Field -->
						<div class="form-group form-group-lg">
							<label class="col-sm-2 control-label">Name</label>
							<div class="col-sm-10 col-md-6">
								<input type="text" name="Name" class="form-control" required="required" placeholder="Name Of The Category" value="<?php echo $cat['Name'] ?>" />
							</div>
						</div>
						<!-- End Name Field -->
						<!-- Start Company Field -->
						<div class="form-group form-group-lg">
							<label class="col-sm-2 control-label">Company</label>
							<div class="col-sm-10 col-md-6">
								<select name="company">
									<?php
										$allCats = getAllFrom("*", "company", "", "", "Id");
										foreach ($allCats as $c) {
											echo "<option value='" . $c['Id'] . "'";
											if ($cat['Id'] == $c['Id']) { echo ' selected'; }
											echo ">" . $c['Name'] . "</option>";
										}
									?>
								</select>
							</div>
						</div>
						<!-- End Company Field -->
						<!-- Start Notes Field -->
						<div class="form-group form-group-lg">
							<label class="col-sm-2 control-label">Notes</label>
							<div class="col-sm-10 col-md-6">
								<input type="text" name="Notes" class="form-control" placeholder="Notes The Category" value="<?php echo $cat['Notes'] ?>"/>
							</div>
						</div>
						<!-- End Notes Field -->
						<!-- Start Submit Field -->
						<div class="form-group form-group-lg">
							<div class="col-sm-offset-2 col-sm-10">
								<input type="submit" value="Save" class="btn btn-primary btn-lg" />
							</div>
						</div>
						<!-- End Submit Field -->
					</form>
				</div>

			<?php

			// If There's No Such ID Show Error Message

			} else {

				echo "<div class='container'>";

				$theMsg = '<div class="alert alert-danger">Theres No Such ID</div>';

				redirectHome($theMsg);

				echo "</div>";
			}

		} elseif ($do == 'Update') {

			echo "<h1 class='text-center'>Update Category</h1>";
			echo "<div class='container'>";

			if ($_SERVER['REQUEST_METHOD'] == 'POST') {

				// Get Variables From The Form

				$id 		= $_POST['catid'];
				$name 		= $_POST['Name'];
				$company 	= $_POST['company'];
				$Notes 		= $_POST['Notes'];

				// Update The Database With This Info

				$stmt = $con->prepare("UPDATE 
											categories 
										SET 
											Name = ?, 
											CompanyID = ?, 
											Notes = ?
										WHERE 
											Id = ?");

				$stmt->execute(array($name, $company, $Notes, $id));

				// Echo Success Message

				$theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Updated</div>';

				redirectHome($theMsg, 'back');

			} else {

				$theMsg = '<div class="alert alert-danger">Sorry You Cant Browse This Page Directly</div>';

				redirectHome($theMsg);

			}

			echo "</div>";

		} elseif ($do == 'Delete') {

			echo "<h1 class='text-center'>Delete Category</h1>";
			echo "<div class='container'>";

				// Check If Get Request Catid Is Numeric & Get The Integer Value Of It

				$catid = isset($_GET['catid']) && is_numeric($_GET['catid']) ? intval($_GET['catid']) : 0;

				// Select All Data Depend On This ID

				$check = checkItem('Id', 'categories', $catid);

				// If There's Such ID Show The Form

				if ($check > 0) {

					$stmt = $con->prepare("DELETE FROM categories WHERE Id = :zid");

					//ربط ال zid مع  catid
					$stmt->bindParam(":zid", $catid);

					$stmt->execute();

					$theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Deleted</div>';

					redirectHome($theMsg, 'back');

				} else {

					$theMsg = '<div class="alert alert-danger">This ID is Not Exist</div>';

					redirectHome($theMsg);

				}

			echo '</div>';

		}

		include $tp1 . 'footer.php';
	
	}else {

		 header('location: index.php');

		 exit();
	
	}

	ob_end_flush(); // Release The Output
?>	
<?php

/*
	** Get All Function v2.0
	** Function To Get All Records From Any Database Table
	*/

	function getAllFrom($field, $table, $where = NULL, $and = NULL, $orderfield, $ordering = "DESC") {

		global $con;

		$getAll = $con->prepare("SELECT $field FROM $table $where $and ORDER BY $orderfield $ordering");

		$getAll->execute();

		$all = $getAll->fetchAll();

		return $all;

	}


	/* v1.0
	** title function that echo the page title in case the page 
	**has the variable $pagetitle and echo defult title for other pages
	*/

	function getTitle(){//هذه التابع من اجل الترويسة في كل صفحة نضع المتغير بيجتايتيل ونعطيه قيمة بحسب اسم الصفحة
		global $PageTitle;

		if (isset($PageTitle)) {
			
			echo $PageTitle;

		}else{

			echo 'Dafault';
		}
	}
	/*v2.0
	**redirect function [this function accept parameters]
	**$themsg= echo the massege [ error | success | warning]
	**$url = the link you want to redirect to
	**$seconds = seconda before redirecting
	**
	*/
	function redirectHome($themsg,$url = null , $seconds =1){

		if ($url === null) {
			
			$url = 'index.php';
			$link = 'homepage';
		}else{
			if (isset($_SERVER['HTTP_REFERER']) && $_SERVER['HTTP_REFERER'] != '') {
				
				$url = $_SERVER['HTTP_REFERER'];
				$link = 'previous page';

			}else{

				$url = 'index.php';
				$link = 'homepage';
			}

			
		}

		echo $themsg;

		echo "<div class='alert alert-info container'>you will be redirected to $link after $seconds seconds.</div>";

		header("refresh:$seconds; url=$url ");

		exit();

	}

	/*v1.0
	===============================
	** check items function
	**function to check item in database [function accept parameters]
	**$select = the item to select [example: user ,item ,category]
	**$from = the table to select from [example: user ,item ,categories]
	**$value = the value of select [example :mohammmad , box, electronics]
	*/
	function checkitem($select ,$from ,$value){

		 global $con;

		 $statement = $con->prepare("SELECT $select FRoM $from WHERE  $select = ? ");
		 
		 $statement->execute(array($value));
		 
		 $count = $statement->rowCount();

		 return $count;


	}

	/*v1.0	
	**count number of items function
	**function to count number of items rows
	**$item = the item to count 
	**$table = the table to choose from
	*/
	function countItems($item ,$table){

		global $con;
		$stmt2 = $con->prepare("SELECT COUNT($item) FROM $table");
		$stmt2 -> execute();

		return $stmt2->fetchColumn();

	}

	/*
	** Get Latest Records Function v1.0
	** Function To Get Latest Items From Database [ Users, Items, Comments ]
	** $select = Field To Select
	** $table = The Table To Choose From
	** $order = The Desc Ordering تنازلی[userid ,catid, itemid الترتيب عن طريق ال]
	** $limit = Number Of Records To Get
	*/

     function getLatest($select, $table, $order, $limit = 5) {

		global $con;

		$getStmt = $con->prepare("SELECT $select FROM $table ORDER BY $order DESC LIMIT $limit");

		$getStmt->execute();

		$rows = $getStmt->fetchAll();

		return $rows;

	}
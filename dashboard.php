<?php
    ob_start(); 
	session_start();
	$pageTitle = 'لوحة التحكم - UNHCR AC System';
	
    include 'init.php';
    
    // التحقق من تسجيل الدخول
    if (!isset($_COOKIE['user'])) {
        header('Location: login.php');
        exit();
    }
?>

<div class="container">
	<div class="row">
		<div class="col-md-12">
			<h1 class="text-center page-header">لوحة التحكم - نظام صيانة المكيفات</h1>
		</div>
	</div>
	
	<!-- إحصائيات سريعة -->
	<div class="row">
		<div class="col-md-3">
			<div class="panel panel-primary">
				<div class="panel-heading">
					<div class="row">
						<div class="col-xs-3">
							<i class="fa fa-snowflake-o fa-5x"></i>
						</div>
						<div class="col-xs-9 text-right">
							<div class="huge">
								<?php 
									$totalACs = countItems('Id', 'ac');
									echo $totalACs;
								?>
							</div>
							<div>إجمالي المكيفات</div>
						</div>
					</div>
				</div>
				<a href="Category.php">
					<div class="panel-footer">
						<span class="pull-left">عرض التفاصيل</span>
						<span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
						<div class="clearfix"></div>
					</div>
				</a>
			</div>
		</div>
		
		<div class="col-md-3">
			<div class="panel panel-green">
				<div class="panel-heading">
					<div class="row">
						<div class="col-xs-3">
							<i class="fa fa-wrench fa-5x"></i>
						</div>
						<div class="col-xs-9 text-right">
							<div class="huge">
								<?php 
									// عدد المكيفات التي تحتاج صيانة
									$needMaintenance = 0;
									$allItems = getAllFrom('*', 'ac','','','Id');
									foreach ($allItems as $item) {
										// يمكن إضافة منطق للتحقق من حالة الصيانة
										$needMaintenance++;
									}
									echo $needMaintenance;
								?>
							</div>
							<div>تحتاج صيانة</div>
						</div>
					</div>
				</div>
				<a href="maintenance.php">
					<div class="panel-footer">
						<span class="pull-left">عرض التفاصيل</span>
						<span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
						<div class="clearfix"></div>
					</div>
				</a>
			</div>
		</div>
		
		<div class="col-md-3">
			<div class="panel panel-yellow">
				<div class="panel-heading">
					<div class="row">
						<div class="col-xs-3">
							<i class="fa fa-users fa-5x"></i>
						</div>
						<div class="col-xs-9 text-right">
							<div class="huge">
								<?php 
									$totalUsers = countItems('userID', 'users');
									echo $totalUsers;
								?>
							</div>
							<div>المستخدمين</div>
						</div>
					</div>
				</div>
				<a href="members.php">
					<div class="panel-footer">
						<span class="pull-left">عرض التفاصيل</span>
						<span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
						<div class="clearfix"></div>
					</div>
				</a>
			</div>
		</div>
		
		<div class="col-md-3">
			<div class="panel panel-red">
				<div class="panel-heading">
					<div class="row">
						<div class="col-xs-3">
							<i class="fa fa-file-excel-o fa-5x"></i>
						</div>
						<div class="col-xs-9 text-right">
							<div class="huge">
								<i class="fa fa-download"></i>
							</div>
							<div>تصدير البيانات</div>
						</div>
					</div>
				</div>
				<a href="export.php">
					<div class="panel-footer">
						<span class="pull-left">تصدير Excel</span>
						<span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
						<div class="clearfix"></div>
					</div>
				</a>
			</div>
		</div>
	</div>
	
	<!-- قائمة المكيفات -->
	<div class="row">
		<div class="col-md-12">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h3 class="panel-title">قائمة أجهزة التكييف</h3>
				</div>
				<div class="panel-body">
					<div class="row">
						<?php
							$allItems = getAllFrom('*', 'ac','','','Id');
							if (!empty($allItems)) {
								foreach ($allItems as $item) {
									echo '<div class="col-sm-6 col-md-4 col-lg-3">';
									echo '<div class="thumbnail item-box">';
										echo '<div class="caption text-center">';
											echo '<h4><a href="maintenance.php?acid='. $item['Id'] .'" class="btn btn-info btn-block">';
											echo '<i class="fa fa-snowflake-o"></i> ' . $item['Name'];
											echo '</a></h4>';
											echo '<p class="text-muted">رقم الجهاز: ' . $item['Id'] . '</p>';
										echo '</div>';
									echo '</div>';
									echo '</div>';
								}
							} else {
								echo '<div class="col-md-12">';
								echo '<div class="alert alert-info text-center">';
								echo '<i class="fa fa-info-circle"></i> لا توجد أجهزة تكييف مسجلة في النظام';
								echo '</div>';
								echo '</div>';
							}
						?>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<?php
	include $tp1 . 'footer.php'; 
	ob_end_flush();
?>

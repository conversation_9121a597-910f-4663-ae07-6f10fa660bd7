<?php
	session_start();
	$pageTitle = 'Homepage';
	
    include 'init.php';
    if (isset($_COOKIE['user'])) {
?>
<div class="container">
	<!--<h1 class="text-center">Welcome</h1>-->
	<div class="row">
		<hr>
		<?php
		if(isset($_GET['search_query'])) {
    $search_query = filter_var($_GET['search_query'],FILTER_SANITIZE_SPECIAL_CHARS);
    
    $stmt = $con->prepare("SELECT * FROM ac WHERE Code LIKE '%$search_query%'");

			// Execute The Statement

			$stmt->execute();

			// Assign To Variable 

			$allItems = $stmt->fetchAll();
 
		 if (! empty($allItems)){
			 	foreach ($allItems as $item) {

					echo '<div class="thumbnail item-box">';
						echo '<div class="caption">';
							echo '<h3><a href="maintenance.php?acid='. $item['Id'] .'">' . $item['Code'] .'</a></h3>';
						echo '</div>';
				echo '</div>';
			}
			 }else {
			 	echo '<div class="container">';
			echo '<div class="alert alert-danger">No Results Found</div>';
		echo '</div>';
    }
}
			
		?>
	</div>
</div>
<?php
} else {
		header('Location: login.php');
		exit();
	}
	include $tp1 . 'footer.php'; 
	ob_end_flush();
?> 	

<?php

	/*
	================================================
	== company Page
	================================================
	*/

	ob_start(); // Output Buffering Start

	session_start();

	$pageTitle = 'Company';

	if (isset($_COOKIE['username'])) {

		include 'init.php';

		$do = isset($_GET['do']) ? $_GET['do'] : 'Manage';

		if ($do == 'Manage') {

			$sort = 'asc';

			$sort_array = array('asc', 'desc');

			if (isset($_GET['sort']) && in_array($_GET['sort'], $sort_array)) {

				$sort = $_GET['sort'];

			}

			$stmt2 = $con->prepare("SELECT * FROM company");

			$stmt2->execute();

			$cats = $stmt2->fetchAll(); 
			?>
			<h1 class="text-center">Manage Company</h1>
			<div class="container categories">
				<div class="panel panel-default">
					<div class="panel-heading"><i class="fa fa-edit"></i> Manage Company
						<div class="option pull-right">
							<i class="fa fa-sort"></i> Ordering: [
							<a class="<?php if ($sort == 'asc') { echo 'active'; } ?>" href="?sort=asc">Asc</a> | 
							<a class="<?php if ($sort == 'desc') { echo 'active'; } ?>" href="?sort=desc">Desc</a> ]
							<i class="fa fa-eye"></i> View: [
							<span class="active" data-view="full">Full</span> |
							<span data-view="classic">Classic</span> ]
						</div>
					<div class="panel-body">
						<?php
							foreach($cats as $cat) {
								echo "<div class='cat'>";
									echo "<div class='hidden-buttons'>";
										echo "<a href='company.php?do=Edit&comid=" . $cat['Id'] . "' class='btn btn-xs btn-primary'><i class='fa fa-edit'></i> Edit</a>";
										echo "<a href='company.php?do=Delete&comid=" . $cat['Id'] . "' class='confirm btn btn-xs btn-danger'><i class='fa fa-close'></i> Delete</a>";
									echo "</div>";
									echo "<h3>" . $cat['Name'] . '</h3>';
									echo "<div class='full-view'>";
										echo "<p>"; if($cat['Address'] == '') { echo 'This Company has no Address'; } else { echo $cat['Address']; } echo "</p>";
                                        echo "<p>"; if($cat['Phone'] == '') { echo 'This Company has no Phone'; } else { echo $cat['Phone']; } echo "</p>";
                                        echo "<p>"; if($cat['Email'] == '') { echo 'This Company has no Email'; } else { echo $cat['Email']; } echo "</p>";
										echo "<p>"; if($cat['Notes'] == '') { echo 'This Company has no Notes'; } else { echo $cat['Notes']; } echo "</p>";

									echo "</div>";
								echo "</div>"; 
								echo "<hr>";

							}
						?>
					</div>
				</div>
				<div class="col-sm-offset-5 col-sm-10">
							<a class="add-category btn btn-primary" href="company.php?do=Add"><i class="fa fa-plus"></i> Add New Company</a>
				</div>
			</div>



<?php		} elseif ($do == 'Add') {?>
			<h1 class="text-center">Add New Company</h1>
			<div class="container">
				<form class="form-horizontal" action="?do=Insert" method="POST">
					<!-- Start Name Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Name</label>
						<div class="col-sm-10 col-md-6">
							<input type="text" name="Name" class="form-control" autocomplete="off" required="required" placeholder="Name Of The Category" />
						</div>
					</div>
					<!-- End Name Field -->
					<!-- Start Address Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Address</label>
						<div class="col-sm-10 col-md-6">
							<input type="text" name="Address" class="form-control" placeholder="Address The Company" />
						</div>
					</div>
					<!-- End Address Field -->
					<!-- Start Phone Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Phone</label>
						<div class="col-sm-10 col-md-6">
							<input type="text" name="Phone" class="form-control" placeholder="Phone The Company" />
						</div>
					</div>
					<!-- End Phone Field -->
                    <!-- Start Email Field -->
                    <div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Email</label>
						<div class="col-sm-10 col-md-6">
							<input type="Email" name="Email" class="form-control" placeholder="Email The Company" />
						</div>
					</div>
					<!-- End Email Field -->
					<!-- Start Notes Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Notes</label>
						<div class="col-sm-10 col-md-6">
							<input type="text" name="Notes" class="form-control" placeholder="Notes The Company" />
						</div>
					</div>
					<!-- End Notes Field -->
					<!-- Start Submit Field -->
					<div class="form-group form-group-lg">
						<div class="col-sm-offset-5 col-sm-10">
							<input type="submit" value="Add Category" class="btn btn-primary btn-lg" />
						</div>
					</div>
					<!-- End Submit Field -->	
				</form>
			</div>





<?php	} elseif ($do == 'Insert') {

			if ($_SERVER['REQUEST_METHOD'] == 'POST') {

				echo "<h1 class='text-center'>Insert Category</h1>";
				echo "<div class='container'>";

				// Get Variables From The Form

				$name 		= $_POST['Name'];
				$address 	= $_POST['Address'];
				$phone  	= $_POST['Phone'];
				$email 		= $_POST['Email'];
				$notes  	= $_POST['Notes'];

				// Check If Category Exist in Database

				$check = checkitem("Name", "company", $name);

				if ($check == 1) {

					$themsg = '<div class="alert alert-danger">Sorry This Company Is Exist</div>';

					redirectHome($themsg, 'back');

				} else {

					// Insert Category Info In Database

					$stmt = $con->prepare("INSERT INTO 

                        company(Name, Address, Phone, Email, Notes)

						VALUES(:zname, :zadd, :zphonet, :zemail, :znotes)");

					$stmt->execute(array(
						'zname' 	=> $name,
						'zadd' 	=> $address,
						'zphonet' 	=> $phone,
						'zemail' 	=> $email,
						'znotes' 	=> $notes,
					));

					// Echo Success Message

					$themsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Inserted</div>';

					redirectHome($themsg, 'back');

				}

			} else {

				echo "<div class='container'>";

				$themsg = '<div class="alert alert-danger">Sorry You Cant Browse This Page Directly</div>';

				redirectHome($themsg, 'back');

					echo "</div>";

			}

			echo "</div>";



		} elseif ($do == 'Edit') {
			// Check If Get Request catid Is Numeric & Get Its Integer Value

			$catid = isset($_GET['comid']) && is_numeric($_GET['comid']) ? intval($_GET['comid']) : 0;

			// Select All Data Depend On This ID

			$stmt = $con->prepare("SELECT * FROM company WHERE Id = ?");

			// Execute Query

			$stmt->execute(array($catid));

			// Fetch The Data

			$cat = $stmt->fetch();

			// The Row Count

			$count = $stmt->rowCount();

			// If There's Such ID Show The Form

			if ($count > 0) { ?>

				<h1 class="text-center">Edit Company</h1>
				<div class="container">
					<form class="form-horizontal" action="?do=Update" method="POST">
						<input type="hidden" name="comid" value="<?php echo $catid ?>" />
						<!-- Start Name Field -->
						<div class="form-group form-group-lg">
							<label class="col-sm-2 control-label">Name</label>
							<div class="col-sm-10 col-md-6">
								<input type="text" name="Name" class="form-control" required="required" placeholder="Name Of The Company" value="<?php echo $cat['Name'] ?>" />
							</div>
						</div>
						<!-- End Name Field -->
						<!-- Start Address Field -->
                        <div class="form-group form-group-lg">
                            <label class="col-sm-2 control-label">Address</label>
                            <div class="col-sm-10 col-md-6">
                                <input type="text" name="Address" class="form-control" placeholder="Address The Company" value="<?php echo $cat['Address'] ?>" />
                            </div>
                        </div>
                        <!-- End Address Field -->
                        <!-- Start Phone Field -->
                        <div class="form-group form-group-lg">
                            <label class="col-sm-2 control-label">Phone</label>
                            <div class="col-sm-10 col-md-6">
                                <input type="text" name="Phone" class="form-control" placeholder="Phone The Company" value="<?php echo $cat['Phone'] ?>"/>
                            </div>
                        </div>
                        <!-- End Phone Field -->
                        <!-- Start Email Field -->
                        <div class="form-group form-group-lg">
                            <label class="col-sm-2 control-label">Email</label>
                            <div class="col-sm-10 col-md-6">
                                <input type="Email" name="Email" class="form-control" placeholder="Email The Company" value="<?php echo $cat['Email'] ?>"/>
                            </div>
                        </div>
                        <!-- End Email Field -->
                        <!-- Start Notes Field -->
                        <div class="form-group form-group-lg">
                            <label class="col-sm-2 control-label">Notes</label>
                            <div class="col-sm-10 col-md-6">
                                <input type="text" name="Notes" class="form-control" placeholder="Notes The Company" value="<?php echo $cat['Notes'] ?>"/>
                            </div>
                        </div>
                        <!-- End Notes Field -->
						<!-- Start Submit Field -->
						<div class="form-group form-group-lg">
							<div class="col-sm-offset-2 col-sm-10">
								<input type="submit" value="Save" class="btn btn-primary btn-lg" />
							</div>
						</div>
						<!-- End Submit Field -->
					</form>
				</div>

			<?php

			// If There's No Such ID Show Error Message

			} else {

				echo "<div class='container'>";

				$theMsg = '<div class="alert alert-danger">Theres No Such ID</div>';

				redirectHome($theMsg);

				echo "</div>";
			}

		} elseif ($do == 'Update') {

			echo "<h1 class='text-center'>Update Category</h1>";
			echo "<div class='container'>";

			if ($_SERVER['REQUEST_METHOD'] == 'POST') {

				// Get Variables From The Form

				$id 		= $_POST['comid'];
				$name 		= $_POST['Name'];
				$address 	= $_POST['Address'];
				$phone  	= $_POST['Phone'];
				$email 		= $_POST['Email'];
				$notes  	= $_POST['Notes'];

				// Update The Database With This Info

				$stmt = $con->prepare("UPDATE 
											company 
										SET 
                                            Name = ?, 
											Address = ?, 
											Phone = ?, 
											Email = ?,
											Notes = ?
										WHERE 
											Id = ?");

				$stmt->execute(array($name, $address, $phone, $email, $notes, $id));
                
				// Echo Success Message

				$theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Updated</div>';

				redirectHome($theMsg, 'back');

			} else {

				$theMsg = '<div class="alert alert-danger">Sorry You Cant Browse This Page Directly</div>';

				redirectHome($theMsg);

			}

			echo "</div>";

		} elseif ($do == 'Delete') {

			echo "<h1 class='text-center'>Delete Company</h1>";
			echo "<div class='container'>";

				// Check If Get Request Catid Is Numeric & Get The Integer Value Of It

				$catid = isset($_GET['comid']) && is_numeric($_GET['comid']) ? intval($_GET['comid']) : 0;

				// Select All Data Depend On This ID

				$check = checkItem('Id', 'company', $catid);

				// If There's Such ID Show The Form

				if ($check > 0) {

					$stmt = $con->prepare("DELETE FROM company WHERE Id = :zid");

					//ربط ال zid مع  catid
					$stmt->bindParam(":zid", $catid);

					$stmt->execute();

					$theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Deleted</div>';

					redirectHome($theMsg, 'back');

				} else {

					$theMsg = '<div class="alert alert-danger">This ID is Not Exist</div>';

					redirectHome($theMsg);

				}

			echo '</div>';

		}

		include $tp1 . 'footer.php';
	
	}else {

		 header('location: index.php');

		 exit();
	
	}

	ob_end_flush(); // Release The Output
?>	
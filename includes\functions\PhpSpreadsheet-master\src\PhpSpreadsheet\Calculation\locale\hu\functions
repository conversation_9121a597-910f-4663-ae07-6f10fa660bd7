############################################################
##
## PhpSpreadsheet - function name translations
##
## <PERSON><PERSON><PERSON> (Hungarian)
##
############################################################


##
## Ko<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ye<PERSON> (Cube Functions)
##
CUBEKPIMEMBER = KOCKA.FŐTELJMUT
CUBEMEMBER = KOCKA.TAG
CUBEMEMBERPROPERTY = KOCKA.TAG.TUL
CUBERANKEDMEMBER = KOCKA.HALM.ELEM
CUBESET = KOCKA.HALM
CUBESETCOUNT = KOCKA.HALM.DB
CUBEVALUE = KOCKA.ÉRTÉK

##
## Adatbázis-k<PERSON><PERSON><PERSON> függvények (Database Functions)
##
DAVERAGE = AB.ÁTLAG
DCOUNT = AB.DARAB
DCOUNTA = AB.DARAB2
DGET = AB.MEZŐ
DMAX = AB.MAX
DMIN = AB.MIN
DPRODUCT = AB.SZORZAT
DSTDEV = AB.SZÓRÁS
DSTDEVP = AB.SZÓRÁS2
DSUM = AB.SZUM
DVAR = AB.VAR
DVARP = AB.VAR2

##
## Dátumfüggvények (Date & Time Functions)
##
DATE = DÁTUM
DATEDIF = DÁTUMTÓLIG
DATESTRING = DÁTUMSZÖVEG
DATEVALUE = DÁTUMÉRTÉK
DAY = NAP
DAYS = NAPOK
DAYS360 = NAP360
EDATE = KALK.DÁTUM
EOMONTH = HÓNAP.UTOLSÓ.NAP
HOUR = ÓRA
ISOWEEKNUM = ISO.HÉT.SZÁMA
MINUTE = PERCEK
MONTH = HÓNAP
NETWORKDAYS = ÖSSZ.MUNKANAP
NETWORKDAYS.INTL = ÖSSZ.MUNKANAP.INTL
NOW = MOST
SECOND = MPERC
THAIDAYOFWEEK = THAIHÉTNAPJA
THAIMONTHOFYEAR = THAIHÓNAP
THAIYEAR = THAIÉV
TIME = IDŐ
TIMEVALUE = IDŐÉRTÉK
TODAY = MA
WEEKDAY = HÉT.NAPJA
WEEKNUM = HÉT.SZÁMA
WORKDAY = KALK.MUNKANAP
WORKDAY.INTL = KALK.MUNKANAP.INTL
YEAR = ÉV
YEARFRAC = TÖRTÉV

##
## Mérnöki függvények (Engineering Functions)
##
BESSELI = BESSELI
BESSELJ = BESSELJ
BESSELK = BESSELK
BESSELY = BESSELY
BIN2DEC = BIN.DEC
BIN2HEX = BIN.HEX
BIN2OCT = BIN.OKT
BITAND = BIT.ÉS
BITLSHIFT = BIT.BAL.ELTOL
BITOR = BIT.VAGY
BITRSHIFT = BIT.JOBB.ELTOL
BITXOR = BIT.XVAGY
COMPLEX = KOMPLEX
CONVERT = KONVERTÁLÁS
DEC2BIN = DEC.BIN
DEC2HEX = DEC.HEX
DEC2OCT = DEC.OKT
DELTA = DELTA
ERF = HIBAF
ERF.PRECISE = HIBAF.PONTOS
ERFC = HIBAF.KOMPLEMENTER
ERFC.PRECISE = HIBAFKOMPLEMENTER.PONTOS
GESTEP = KÜSZÖBNÉL.NAGYOBB
HEX2BIN = HEX.BIN
HEX2DEC = HEX.DEC
HEX2OCT = HEX.OKT
IMABS = KÉPZ.ABSZ
IMAGINARY = KÉPZETES
IMARGUMENT = KÉPZ.ARGUMENT
IMCONJUGATE = KÉPZ.KONJUGÁLT
IMCOS = KÉPZ.COS
IMCOSH = KÉPZ.COSH
IMCOT = KÉPZ.COT
IMCSC = KÉPZ.CSC
IMCSCH = KÉPZ.CSCH
IMDIV = KÉPZ.HÁNYAD
IMEXP = KÉPZ.EXP
IMLN = KÉPZ.LN
IMLOG10 = KÉPZ.LOG10
IMLOG2 = KÉPZ.LOG2
IMPOWER = KÉPZ.HATV
IMPRODUCT = KÉPZ.SZORZAT
IMREAL = KÉPZ.VALÓS
IMSEC = KÉPZ.SEC
IMSECH = KÉPZ.SECH
IMSIN = KÉPZ.SIN
IMSINH = KÉPZ.SINH
IMSQRT = KÉPZ.GYÖK
IMSUB = KÉPZ.KÜL
IMSUM = KÉPZ.ÖSSZEG
IMTAN = KÉPZ.TAN
OCT2BIN = OKT.BIN
OCT2DEC = OKT.DEC
OCT2HEX = OKT.HEX

##
## Pénzügyi függvények (Financial Functions)
##
ACCRINT = IDŐSZAKI.KAMAT
ACCRINTM = LEJÁRATI.KAMAT
AMORDEGRC = ÉRTÉKCSÖKK.TÉNYEZŐVEL
AMORLINC = ÉRTÉKCSÖKK
COUPDAYBS = SZELVÉNYIDŐ.KEZDETTŐL
COUPDAYS = SZELVÉNYIDŐ
COUPDAYSNC = SZELVÉNYIDŐ.KIFIZETÉSTŐL
COUPNCD = ELSŐ.SZELVÉNYDÁTUM
COUPNUM = SZELVÉNYSZÁM
COUPPCD = UTOLSÓ.SZELVÉNYDÁTUM
CUMIPMT = ÖSSZES.KAMAT
CUMPRINC = ÖSSZES.TŐKERÉSZ
DB = KCS2
DDB = KCSA
DISC = LESZÁM
DOLLARDE = FORINT.DEC
DOLLARFR = FORINT.TÖRT
DURATION = KAMATÉRZ
EFFECT = TÉNYLEGES
FV = JBÉ
FVSCHEDULE = KJÉ
INTRATE = KAMATRÁTA
IPMT = RRÉSZLET
IRR = BMR
ISPMT = LRÉSZLETKAMAT
MDURATION = MKAMATÉRZ
MIRR = MEGTÉRÜLÉS
NOMINAL = NÉVLEGES
NPER = PER.SZÁM
NPV = NMÉ
ODDFPRICE = ELTÉRŐ.EÁR
ODDFYIELD = ELTÉRŐ.EHOZAM
ODDLPRICE = ELTÉRŐ.UÁR
ODDLYIELD = ELTÉRŐ.UHOZAM
PDURATION = KAMATÉRZ.PER
PMT = RÉSZLET
PPMT = PRÉSZLET
PRICE = ÁR
PRICEDISC = ÁR.LESZÁM
PRICEMAT = ÁR.LEJÁRAT
PV = MÉ
RATE = RÁTA
RECEIVED = KAPOTT
RRI = MR
SLN = LCSA
SYD = ÉSZÖ
TBILLEQ = KJEGY.EGYENÉRT
TBILLPRICE = KJEGY.ÁR
TBILLYIELD = KJEGY.HOZAM
VDB = ÉCSRI
XIRR = XBMR
XNPV = XNJÉ
YIELD = HOZAM
YIELDDISC = HOZAM.LESZÁM
YIELDMAT = HOZAM.LEJÁRAT

##
## Információs függvények (Information Functions)
##
CELL = CELLA
ERROR.TYPE = HIBA.TÍPUS
INFO = INFÓ
ISBLANK = ÜRES
ISERR = HIBA.E
ISERROR = HIBÁS
ISEVEN = PÁROSE
ISFORMULA = KÉPLET
ISLOGICAL = LOGIKAI
ISNA = NINCS
ISNONTEXT = NEM.SZÖVEG
ISNUMBER = SZÁM
ISODD = PÁRATLANE
ISREF = HIVATKOZÁS
ISTEXT = SZÖVEG.E
N = S
NA = HIÁNYZIK
SHEET = LAP
SHEETS = LAPOK
TYPE = TÍPUS

##
## Logikai függvények (Logical Functions)
##
AND = ÉS
FALSE = HAMIS
IF = HA
IFERROR = HAHIBA
IFNA = HAHIÁNYZIK
IFS = HAELSŐIGAZ
NOT = NEM
OR = VAGY
SWITCH = ÁTVÁLT
TRUE = IGAZ
XOR = XVAGY

##
## Keresési és hivatkozási függvények (Lookup & Reference Functions)
##
ADDRESS = CÍM
AREAS = TERÜLET
CHOOSE = VÁLASZT
COLUMN = OSZLOP
COLUMNS = OSZLOPOK
FORMULATEXT = KÉPLETSZÖVEG
GETPIVOTDATA = KIMUTATÁSADATOT.VESZ
HLOOKUP = VKERES
HYPERLINK = HIPERHIVATKOZÁS
INDEX = INDEX
INDIRECT = INDIREKT
LOOKUP = KERES
MATCH = HOL.VAN
OFFSET = ELTOLÁS
ROW = SOR
ROWS = SOROK
RTD = VIA
TRANSPOSE = TRANSZPONÁLÁS
VLOOKUP = FKERES
*RC = SO

##
## Matematikai és trigonometrikus függvények (Math & Trig Functions)
##
ABS = ABS
ACOS = ARCCOS
ACOSH = ACOSH
ACOT = ARCCOT
ACOTH = ARCCOTH
AGGREGATE = ÖSSZESÍT
ARABIC = ARAB
ASIN = ARCSIN
ASINH = ASINH
ATAN = ARCTAN
ATAN2 = ARCTAN2
ATANH = ATANH
BASE = ALAP
CEILING.MATH = PLAFON.MAT
CEILING.PRECISE = PLAFON.PONTOS
COMBIN = KOMBINÁCIÓK
COMBINA = KOMBINÁCIÓK.ISM
COS = COS
COSH = COSH
COT = COT
COTH = COTH
CSC = CSC
CSCH = CSCH
DECIMAL = TIZEDES
DEGREES = FOK
ECMA.CEILING = ECMA.PLAFON
EVEN = PÁROS
EXP = KITEVŐ
FACT = FAKT
FACTDOUBLE = FAKTDUPLA
FLOOR.MATH = PADLÓ.MAT
FLOOR.PRECISE = PADLÓ.PONTOS
GCD = LKO
INT = INT
ISO.CEILING = ISO.PLAFON
LCM = LKT
LN = LN
LOG = LOG
LOG10 = LOG10
MDETERM = MDETERM
MINVERSE = INVERZ.MÁTRIX
MMULT = MSZORZAT
MOD = MARADÉK
MROUND = TÖBBSZ.KEREKÍT
MULTINOMIAL = SZORHÁNYFAKT
MUNIT = MMÁTRIX
ODD = PÁRATLAN
PI = PI
POWER = HATVÁNY
PRODUCT = SZORZAT
QUOTIENT = KVÓCIENS
RADIANS = RADIÁN
RAND = VÉL
RANDBETWEEN = VÉLETLEN.KÖZÖTT
ROMAN = RÓMAI
ROUND = KEREKÍTÉS
ROUNDBAHTDOWN = BAHTKEREK.LE
ROUNDBAHTUP = BAHTKEREK.FEL
ROUNDDOWN = KEREK.LE
ROUNDUP = KEREK.FEL
SEC = SEC
SECH = SECH
SERIESSUM = SORÖSSZEG
SIGN = ELŐJEL
SIN = SIN
SINH = SINH
SQRT = GYÖK
SQRTPI = GYÖKPI
SUBTOTAL = RÉSZÖSSZEG
SUM = SZUM
SUMIF = SZUMHA
SUMIFS = SZUMHATÖBB
SUMPRODUCT = SZORZATÖSSZEG
SUMSQ = NÉGYZETÖSSZEG
SUMX2MY2 = SZUMX2BŐLY2
SUMX2PY2 = SZUMX2MEGY2
SUMXMY2 = SZUMXBŐLY2
TAN = TAN
TANH = TANH
TRUNC = CSONK

##
## Statisztikai függvények (Statistical Functions)
##
AVEDEV = ÁTL.ELTÉRÉS
AVERAGE = ÁTLAG
AVERAGEA = ÁTLAGA
AVERAGEIF = ÁTLAGHA
AVERAGEIFS = ÁTLAGHATÖBB
BETA.DIST = BÉTA.ELOSZL
BETA.INV = BÉTA.INVERZ
BINOM.DIST = BINOM.ELOSZL
BINOM.DIST.RANGE = BINOM.ELOSZL.TART
BINOM.INV = BINOM.INVERZ
CHISQ.DIST = KHINÉGYZET.ELOSZLÁS
CHISQ.DIST.RT = KHINÉGYZET.ELOSZLÁS.JOBB
CHISQ.INV = KHINÉGYZET.INVERZ
CHISQ.INV.RT = KHINÉGYZET.INVERZ.JOBB
CHISQ.TEST = KHINÉGYZET.PRÓBA
CONFIDENCE.NORM = MEGBÍZHATÓSÁG.NORM
CONFIDENCE.T = MEGBÍZHATÓSÁG.T
CORREL = KORREL
COUNT = DARAB
COUNTA = DARAB2
COUNTBLANK = DARABÜRES
COUNTIF = DARABTELI
COUNTIFS = DARABHATÖBB
COVARIANCE.P = KOVARIANCIA.S
COVARIANCE.S = KOVARIANCIA.M
DEVSQ = SQ
EXPON.DIST = EXP.ELOSZL
F.DIST = F.ELOSZL
F.DIST.RT = F.ELOSZLÁS.JOBB
F.INV = F.INVERZ
F.INV.RT = F.INVERZ.JOBB
F.TEST = F.PRÓB
FISHER = FISHER
FISHERINV = INVERZ.FISHER
FORECAST.ETS = ELŐREJELZÉS.ESIM
FORECAST.ETS.CONFINT = ELŐREJELZÉS.ESIM.KONFINT
FORECAST.ETS.SEASONALITY = ELŐREJELZÉS.ESIM.SZEZONALITÁS
FORECAST.ETS.STAT = ELŐREJELZÉS.ESIM.STAT
FORECAST.LINEAR = ELŐREJELZÉS.LINEÁRIS
FREQUENCY = GYAKORISÁG
GAMMA = GAMMA
GAMMA.DIST = GAMMA.ELOSZL
GAMMA.INV = GAMMA.INVERZ
GAMMALN = GAMMALN
GAMMALN.PRECISE = GAMMALN.PONTOS
GAUSS = GAUSS
GEOMEAN = MÉRTANI.KÖZÉP
GROWTH = NÖV
HARMEAN = HARM.KÖZÉP
HYPGEOM.DIST = HIPGEOM.ELOSZLÁS
INTERCEPT = METSZ
KURT = CSÚCSOSSÁG
LARGE = NAGY
LINEST = LIN.ILL
LOGEST = LOG.ILL
LOGNORM.DIST = LOGNORM.ELOSZLÁS
LOGNORM.INV = LOGNORM.INVERZ
MAX = MAX
MAXA = MAXA
MAXIFS = MAXHA
MEDIAN = MEDIÁN
MIN = MIN
MINA = MIN2
MINIFS = MINHA
MODE.MULT = MÓDUSZ.TÖBB
MODE.SNGL = MÓDUSZ.EGY
NEGBINOM.DIST = NEGBINOM.ELOSZLÁS
NORM.DIST = NORM.ELOSZLÁS
NORM.INV = NORM.INVERZ
NORM.S.DIST = NORM.S.ELOSZLÁS
NORM.S.INV = NORM.S.INVERZ
PEARSON = PEARSON
PERCENTILE.EXC = PERCENTILIS.KIZÁR
PERCENTILE.INC = PERCENTILIS.TARTALMAZ
PERCENTRANK.EXC = SZÁZALÉKRANG.KIZÁR
PERCENTRANK.INC = SZÁZALÉKRANG.TARTALMAZ
PERMUT = VARIÁCIÓK
PERMUTATIONA = VARIÁCIÓK.ISM
PHI = FI
POISSON.DIST = POISSON.ELOSZLÁS
PROB = VALÓSZÍNŰSÉG
QUARTILE.EXC = KVARTILIS.KIZÁR
QUARTILE.INC = KVARTILIS.TARTALMAZ
RANK.AVG = RANG.ÁTL
RANK.EQ = RANG.EGY
RSQ = RNÉGYZET
SKEW = FERDESÉG
SKEW.P = FERDESÉG.P
SLOPE = MEREDEKSÉG
SMALL = KICSI
STANDARDIZE = NORMALIZÁLÁS
STDEV.P = SZÓR.S
STDEV.S = SZÓR.M
STDEVA = SZÓRÁSA
STDEVPA = SZÓRÁSPA
STEYX = STHIBAYX
T.DIST = T.ELOSZL
T.DIST.2T = T.ELOSZLÁS.2SZ
T.DIST.RT = T.ELOSZLÁS.JOBB
T.INV = T.INVERZ
T.INV.2T = T.INVERZ.2SZ
T.TEST = T.PRÓB
TREND = TREND
TRIMMEAN = RÉSZÁTLAG
VAR.P = VAR.S
VAR.S = VAR.M
VARA = VARA
VARPA = VARPA
WEIBULL.DIST = WEIBULL.ELOSZLÁS
Z.TEST = Z.PRÓB

##
## Szövegműveletekhez használható függvények (Text Functions)
##
BAHTTEXT = BAHTSZÖVEG
CHAR = KARAKTER
CLEAN = TISZTÍT
CODE = KÓD
CONCAT = FŰZ
DOLLAR = FORINT
EXACT = AZONOS
FIND = SZÖVEG.TALÁL
FIXED = FIX
ISTHAIDIGIT = ON.THAI.NUMERO
LEFT = BAL
LEN = HOSSZ
LOWER = KISBETŰ
MID = KÖZÉP
NUMBERSTRING = SZÁM.BETŰVEL
NUMBERVALUE = SZÁMÉRTÉK
PHONETIC = FONETIKUS
PROPER = TNÉV
REPLACE = CSERE
REPT = SOKSZOR
RIGHT = JOBB
SEARCH = SZÖVEG.KERES
SUBSTITUTE = HELYETTE
T = T
TEXT = SZÖVEG
TEXTJOIN = SZÖVEGÖSSZEFŰZÉS
THAIDIGIT = THAISZÁM
THAINUMSOUND = THAISZÁMHANG
THAINUMSTRING = THAISZÁMKAR
THAISTRINGLENGTH = THAIKARHOSSZ
TRIM = KIMETSZ
UNICHAR = UNIKARAKTER
UNICODE = UNICODE
UPPER = NAGYBETŰS
VALUE = ÉRTÉK

##
## Webes függvények (Web Functions)
##
ENCODEURL = URL.KÓDOL
FILTERXML = XMLSZŰRÉS
WEBSERVICE = WEBSZOLGÁLTATÁS

##
## Kompatibilitási függvények (Compatibility Functions)
##
BETADIST = BÉTA.ELOSZLÁS
BETAINV = INVERZ.BÉTA
BINOMDIST = BINOM.ELOSZLÁS
CEILING = PLAFON
CHIDIST = KHI.ELOSZLÁS
CHIINV = INVERZ.KHI
CHITEST = KHI.PRÓBA
CONCATENATE = ÖSSZEFŰZ
CONFIDENCE = MEGBÍZHATÓSÁG
COVAR = KOVAR
CRITBINOM = KRITBINOM
EXPONDIST = EXP.ELOSZLÁS
FDIST = F.ELOSZLÁS
FINV = INVERZ.F
FLOOR = PADLÓ
FORECAST = ELŐREJELZÉS
FTEST = F.PRÓBA
GAMMADIST = GAMMA.ELOSZLÁS
GAMMAINV = INVERZ.GAMMA
HYPGEOMDIST = HIPERGEOM.ELOSZLÁS
LOGINV = INVERZ.LOG.ELOSZLÁS
LOGNORMDIST = LOG.ELOSZLÁS
MODE = MÓDUSZ
NEGBINOMDIST = NEGBINOM.ELOSZL
NORMDIST = NORM.ELOSZL
NORMINV = INVERZ.NORM
NORMSDIST = STNORMELOSZL
NORMSINV = INVERZ.STNORM
PERCENTILE = PERCENTILIS
PERCENTRANK = SZÁZALÉKRANG
POISSON = POISSON
QUARTILE = KVARTILIS
RANK = SORSZÁM
STDEV = SZÓRÁS
STDEVP = SZÓRÁSP
TDIST = T.ELOSZLÁS
TINV = INVERZ.T
TTEST = T.PRÓBA
VAR = VAR
VARP = VARP
WEIBULL = WEIBULL
ZTEST = Z.PRÓBA

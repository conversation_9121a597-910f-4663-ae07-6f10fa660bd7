/* start Main RULEZ */
body{
	background-color: #F4F4F4;
	font-size: 16px;
	font-family: 'Arial', sans-serif;
}

h1{
	font-size: 55px;
	margin: 20px 0px;
	font-weight: bold;
	color: #666;
}

/* Homepage Styles */
.jumbotron {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border-radius: 15px;
	margin-bottom: 30px;
	box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.jumbotron h1 {
	font-size: 2.5rem;
	margin-bottom: 20px;
}

.jumbotron .lead {
	font-size: 1.2rem;
	margin-bottom: 20px;
}

.card {
	border: none;
	border-radius: 10px;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	transition: transform 0.3s ease;
	margin-bottom: 20px;
}

.card:hover {
	transform: translateY(-5px);
	box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-body {
	padding: 20px;
}

.card-title {
	color: #333;
	font-weight: bold;
	margin-bottom: 15px;
}

.card-text {
	color: #666;
	margin-bottom: 20px;
}

/* Dashboard Styles */
.page-header {
	border-bottom: 3px solid #337ab7;
	padding-bottom: 15px;
	margin-bottom: 30px;
}

.panel {
	border-radius: 10px;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	border: none;
	margin-bottom: 20px;
}

.panel-heading {
	border-radius: 10px 10px 0 0;
	padding: 15px;
}

.panel-primary .panel-heading {
	background: linear-gradient(45deg, #007bff, #0056b3);
}

.panel-green .panel-heading {
	background: linear-gradient(45deg, #28a745, #1e7e34);
}

.panel-yellow .panel-heading {
	background: linear-gradient(45deg, #ffc107, #e0a800);
}

.panel-red .panel-heading {
	background: linear-gradient(45deg, #dc3545, #c82333);
}

.huge {
	font-size: 40px;
	font-weight: bold;
}

.panel-footer {
	background-color: rgba(0,0,0,0.05);
	border-radius: 0 0 10px 10px;
}

.panel-footer a {
	color: #333;
	text-decoration: none;
}

.panel-footer:hover {
	background-color: rgba(0,0,0,0.1);
}

.item-box {
	transition: transform 0.3s ease;
	border-radius: 10px;
	border: none;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.item-box:hover {
	transform: translateY(-3px);
	box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

/* Button Loading Animation */
.btn.loading {
	position: relative;
	color: transparent;
}

.btn.loading:after {
	content: "";
	position: absolute;
	width: 16px;
	height: 16px;
	top: 50%;
	left: 50%;
	margin-left: -8px;
	margin-top: -8px;
	border-radius: 50%;
	border: 2px solid transparent;
	border-top-color: #ffffff;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* Hover Effects */
.hover-effect {
	transform: scale(1.02);
	transition: all 0.3s ease;
}

/* Fade In Animation */
.fade-in {
	opacity: 0;
	position: relative;
	top: 20px;
	transition: all 0.5s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
	.jumbotron h1 {
		font-size: 1.8rem;
	}

	.jumbotron .lead {
		font-size: 1rem;
	}

	.huge {
		font-size: 30px;
	}

	.panel-heading .col-xs-3 i {
		font-size: 3em !important;
	}
}

/* Arabic Text Support */
.rtl {
	direction: rtl;
	text-align: right;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
	width: 8px;
}

::-webkit-scrollbar-track {
	background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
	background: #888;
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: #555;
}

.input-container {
	position: relative;
}

.asterisk{
	 font-size: 20px;
    position: absolute;
    right: 10px;
    top: 7px;
    color: #D20707;
}

.main-form .asterisk {
    font-size: 30px;
    position: absolute;
    right: 30px;
    top: 8px;
    color: #D20707;
}

.nice-message {
	padding: 10px;
	background-color: #FFF;
	margin: 10px 0;
	border-left: 5px solid #080;


}

/* End Main RULEZ */


/* start bootstrap edits */
.navbar{
	border-radius: 0;
	margin-bottom: 0; 
}

.nav > li > a,
.navbar-brand {
	padding: 15px 12px;

}

.navbar-inverse .navbar-nav > .open > a, 
.navbar-inverse .navbar-nav > .open > a:focus, 
.navbar-inverse .navbar-nav > .open > a:hover,
.dropdown-menu {

    background-color: #3498db;

}
.dropdown-menu{
	padding: 0px;
	font-size: 1em;/*ياخد حجم الخط من الbody */
	border: none;
}
.dropdown-menu > li > a {
	color: #FFF;
	padding: 5px 10px;
}
.dropdown-menu > li > a:focus, 
.dropdown-menu > li > a:hover {
    color: #9d9d9d;
	background-color: #222;
    padding: 5px 20px;

}
.form-control{
	position: relative;

}
.navbar-right ~ .navbar-right {
    margin-top: 10px;
  }
/* End bootstrap edits */

/* Start Header */

.upper-bar {
	padding: 10px;
	background-color: #FFF
}

.my-image {
	width: 32px;
	height: 32px;
}

/* End Header */

/* Start Login Page */

.login-page form,
.the-errors {
	max-width: 380px;
	margin: auto;
}

.login-page form input {
	margin-bottom: 10px;
}

.login-page [data-class="login"].selected {
	color: #337AB7;
}

.login-page [data-class="signup"].selected {
	color: #5cb85c;
}

.login-page h1 {
	color: #C0C0C0;
}

.login-page h1 span {
	cursor: pointer;
}

.login-page .signup {
	display: none;
}

.the-errors .msg {
    padding: 10px;
    text-align: left;
    background-color: #fff;
    margin-bottom: 8px;
    border-right: 1px solid #e0e0e0;
    border-top: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
    color: #919191;
}

.the-errors .error {
    border-left: 5px solid #cd6858
}

.the-errors .success {
    border-left: 5px solid #8BD68B
}


/* End Login Page */

/* Start Categories Page */

.item-box {
	position: relative;
}

.item-box .price-tag {
    background-color: #B4B4B4;
    padding: 2px 10px;
    position: absolute;
    left: 0;
    top: 10px;
    font-weight: bold;
    color: #FFF;
}

.item-box .approve-status {
    position: absolute;
    top: 40px;
    left: 0;
    background-color: #b85a5a;
    color: #FFF;
    padding: 3px 5px;
}

.item-box .caption p {
	height: 44px;
	max-height: 44px;
}

/* End Categories Page */

/* Start Profile Page */

.information {
	margin-top: 20px
}

.information ul {
	padding: 0;
	margin: 0;
}

.information ul li {
	padding: 10px;
}

.information ul li:nth-child(odd) {
	background-color: #EEE;
}

.information ul li span {
	display: inline-block;
	width: 120px;
}

.thumbnail .date {
	text-align: right;
	font-size: 13px;
	color: #AAA;
	font-weight: bold;
}

.information .btn {
	margin-top: 10px;
}

/* End Profile Page */

/* Start Show Item Page */

.item-info h2 {
	padding: 10px;
	margin: 0;
}

.item-info p {
	padding: 10px;
}

.item-info ul li { 
	padding: 10px;
}

.item-info ul li:nth-child(odd) {
	background-color: #e8e8e8;
}

.item-info ul li span {
	display: inline-block;
	width: 120px;
}

.tags-items a {
    display: inline-block;
    background-color: #e2e2e2;
    padding: 2px 10px;
    border-radius: 5px;
    color: #666;
    margin-right: 5px;
}

.add-comment h3 {
	margin: 0 0 10px;
}

.add-comment textarea {
	display: block;
	margin-bottom: 10px;
	width: 500px;
	height: 120px;
}

.comment-box {
	margin-bottom: 20px;
}

.comment-box img {
	max-width: 100px;
	margin-bottom: 10px;
}

.comment-box .profile {
	background-color: #e0e0e0;
	position: relative;
	padding: 10px;
	margin-top: 0px;
}

.comment-box .lead {
	background-color: #e0e0e0;
	position: relative;
	padding: 10px;
	margin-top: 25px;
}

.comment-box .lead:before {
    content: "";
    width: 0;
    height: 0;
    border-width: 15px;
    border-style: solid;
    border-color: transparent #e0e0e0 transparent transparent;
    position: absolute;
    left: -28px;
    top: 10px;
}

/* End Show Item Page */

/* Start Our Custom */

.custom-hr {
	border-top: 1px solid #c9c9c9;
}


/* End Our Custom */
.logo-image {
	width: 128px;
	height: 128px;
}

.logo-left {
  margin: 2cm;
}

header {
    background-color: #f4f4f4;
    color: #000;
    padding: 20px;
    text-align: center;
}

.image-container {
    display: flex;
    justify-content: center;
}

.image-container img {
    width: 200px;
    height: auto;
    margin: 10px;
}

.content {
    text-align: center;
    padding: 20px;
}

.name{
	font-size: 50px;
	
}
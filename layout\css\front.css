/* start Main RULEZ */
body{
	background-color: #F4F4F4;
	font-size: 16px;
	font-family: 'Arial', sans-serif;
}

h1{
	font-size: 55px;
	margin: 20px 0px;
	font-weight: bold;
	color: #666;
}

/* Homepage Styles */
.jumbotron {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border-radius: 15px;
	margin-bottom: 30px;
	box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.jumbotron h1 {
	font-size: 2.5rem;
	margin-bottom: 20px;
}

.jumbotron .lead {
	font-size: 1.2rem;
	margin-bottom: 20px;
}

.card {
	border: none;
	border-radius: 10px;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	transition: transform 0.3s ease;
	margin-bottom: 20px;
}

.card:hover {
	transform: translateY(-5px);
	box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-body {
	padding: 20px;
}

.card-title {
	color: #333;
	font-weight: bold;
	margin-bottom: 15px;
}

.card-text {
	color: #666;
	margin-bottom: 20px;
}

/* Dashboard Styles */
.page-header {
	border-bottom: 3px solid #337ab7;
	padding-bottom: 15px;
	margin-bottom: 30px;
}

.panel {
	border-radius: 10px;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	border: none;
	margin-bottom: 20px;
}

.panel-heading {
	border-radius: 10px 10px 0 0;
	padding: 15px;
}

.panel-primary .panel-heading {
	background: linear-gradient(45deg, #007bff, #0056b3);
}

.panel-green .panel-heading {
	background: linear-gradient(45deg, #28a745, #1e7e34);
}

.panel-yellow .panel-heading {
	background: linear-gradient(45deg, #ffc107, #e0a800);
}

.panel-red .panel-heading {
	background: linear-gradient(45deg, #dc3545, #c82333);
}

.huge {
	font-size: 40px;
	font-weight: bold;
}

.panel-footer {
	background-color: rgba(0,0,0,0.05);
	border-radius: 0 0 10px 10px;
}

.panel-footer a {
	color: #333;
	text-decoration: none;
}

.panel-footer:hover {
	background-color: rgba(0,0,0,0.1);
}

.item-box {
	transition: transform 0.3s ease;
	border-radius: 10px;
	border: none;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.item-box:hover {
	transform: translateY(-3px);
	box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

/* Button Loading Animation */
.btn.loading {
	position: relative;
	color: transparent;
}

.btn.loading:after {
	content: "";
	position: absolute;
	width: 16px;
	height: 16px;
	top: 50%;
	left: 50%;
	margin-left: -8px;
	margin-top: -8px;
	border-radius: 50%;
	border: 2px solid transparent;
	border-top-color: #ffffff;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* Hover Effects */
.hover-effect {
	transform: scale(1.02);
	transition: all 0.3s ease;
}

/* Fade In Animation */
.fade-in {
	opacity: 0;
	position: relative;
	top: 20px;
	transition: all 0.5s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
	.jumbotron h1 {
		font-size: 1.8rem;
	}

	.jumbotron .lead {
		font-size: 1rem;
	}

	.huge {
		font-size: 30px;
	}

	.panel-heading .col-xs-3 i {
		font-size: 3em !important;
	}
}

/* Arabic Text Support */
.rtl {
	direction: rtl;
	text-align: right;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
	width: 8px;
}

::-webkit-scrollbar-track {
	background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
	background: #888;
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: #555;
}

.input-container {
	position: relative;
}

.asterisk{
	 font-size: 20px;
    position: absolute;
    right: 10px;
    top: 7px;
    color: #D20707;
}

.main-form .asterisk {
    font-size: 30px;
    position: absolute;
    right: 30px;
    top: 8px;
    color: #D20707;
}

.nice-message {
	padding: 10px;
	background-color: #FFF;
	margin: 10px 0;
	border-left: 5px solid #080;


}

/* End Main RULEZ */


/* start bootstrap edits */
.navbar{
	border-radius: 0;
	margin-bottom: 0; 
}

.nav > li > a,
.navbar-brand {
	padding: 15px 12px;

}

.navbar-inverse .navbar-nav > .open > a, 
.navbar-inverse .navbar-nav > .open > a:focus, 
.navbar-inverse .navbar-nav > .open > a:hover,
.dropdown-menu {

    background-color: #3498db;

}
.dropdown-menu{
	padding: 0px;
	font-size: 1em;/*ياخد حجم الخط من الbody */
	border: none;
}
.dropdown-menu > li > a {
	color: #FFF;
	padding: 5px 10px;
}
.dropdown-menu > li > a:focus, 
.dropdown-menu > li > a:hover {
    color: #9d9d9d;
	background-color: #222;
    padding: 5px 20px;

}
.form-control{
	position: relative;

}
.navbar-right ~ .navbar-right {
    margin-top: 10px;
  }
/* End bootstrap edits */

/* Start Header */

.upper-bar {
	padding: 10px;
	background-color: #FFF
}

.my-image {
	width: 32px;
	height: 32px;
}

/* End Header */

/* Start Login Page */

.login-page form,
.the-errors {
	max-width: 380px;
	margin: auto;
}

.login-page form input {
	margin-bottom: 10px;
}

.login-page [data-class="login"].selected {
	color: #337AB7;
}

.login-page [data-class="signup"].selected {
	color: #5cb85c;
}

.login-page h1 {
	color: #C0C0C0;
}

.login-page h1 span {
	cursor: pointer;
}

.login-page .signup {
	display: none;
}

.the-errors .msg {
    padding: 10px;
    text-align: left;
    background-color: #fff;
    margin-bottom: 8px;
    border-right: 1px solid #e0e0e0;
    border-top: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
    color: #919191;
}

.the-errors .error {
    border-left: 5px solid #cd6858
}

.the-errors .success {
    border-left: 5px solid #8BD68B
}


/* End Login Page */

/* Start Categories Page */

.item-box {
	position: relative;
}

.item-box .price-tag {
    background-color: #B4B4B4;
    padding: 2px 10px;
    position: absolute;
    left: 0;
    top: 10px;
    font-weight: bold;
    color: #FFF;
}

.item-box .approve-status {
    position: absolute;
    top: 40px;
    left: 0;
    background-color: #b85a5a;
    color: #FFF;
    padding: 3px 5px;
}

.item-box .caption p {
	height: 44px;
	max-height: 44px;
}

/* End Categories Page */

/* Start Profile Page */

.information {
	margin-top: 20px
}

.information ul {
	padding: 0;
	margin: 0;
}

.information ul li {
	padding: 10px;
}

.information ul li:nth-child(odd) {
	background-color: #EEE;
}

.information ul li span {
	display: inline-block;
	width: 120px;
}

.thumbnail .date {
	text-align: right;
	font-size: 13px;
	color: #AAA;
	font-weight: bold;
}

.information .btn {
	margin-top: 10px;
}

/* End Profile Page */

/* Start Show Item Page */

.item-info h2 {
	padding: 10px;
	margin: 0;
}

.item-info p {
	padding: 10px;
}

.item-info ul li { 
	padding: 10px;
}

.item-info ul li:nth-child(odd) {
	background-color: #e8e8e8;
}

.item-info ul li span {
	display: inline-block;
	width: 120px;
}

.tags-items a {
    display: inline-block;
    background-color: #e2e2e2;
    padding: 2px 10px;
    border-radius: 5px;
    color: #666;
    margin-right: 5px;
}

.add-comment h3 {
	margin: 0 0 10px;
}

.add-comment textarea {
	display: block;
	margin-bottom: 10px;
	width: 500px;
	height: 120px;
}

.comment-box {
	margin-bottom: 20px;
}

.comment-box img {
	max-width: 100px;
	margin-bottom: 10px;
}

.comment-box .profile {
	background-color: #e0e0e0;
	position: relative;
	padding: 10px;
	margin-top: 0px;
}

.comment-box .lead {
	background-color: #e0e0e0;
	position: relative;
	padding: 10px;
	margin-top: 25px;
}

.comment-box .lead:before {
    content: "";
    width: 0;
    height: 0;
    border-width: 15px;
    border-style: solid;
    border-color: transparent #e0e0e0 transparent transparent;
    position: absolute;
    left: -28px;
    top: 10px;
}

/* End Show Item Page */

/* Start Our Custom */

.custom-hr {
	border-top: 1px solid #c9c9c9;
}


/* End Our Custom */
.logo-image {
	width: 128px;
	height: 128px;
}

.logo-left {
  margin: 2cm;
}

header {
    background-color: #f4f4f4;
    color: #000;
    padding: 20px;
    text-align: center;
}

.image-container {
    display: flex;
    justify-content: center;
}

.image-container img {
    width: 200px;
    height: auto;
    margin: 10px;
}

.content {
    text-align: center;
    padding: 20px;
}

.name{
	font-size: 50px;

}

/* Professional Homepage Styles for Sabri Youssef Company */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap');

/* Global Styles */
html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Tajawal', 'Arial', sans-serif;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Hero Section */
.hero-section {
    position: relative;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-content {
    color: white;
    padding: 2rem 0;
}

.hero-title {
    font-size: 4rem;
    font-weight: 900;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffd700;
    margin-bottom: 2rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-description {
    font-size: 1.3rem;
    line-height: 1.8;
    margin-bottom: 3rem;
    opacity: 0.9;
    animation: fadeInUp 1s ease-out 0.4s both;
}

.hero-buttons {
    animation: fadeInUp 1s ease-out 0.6s both;
}

.hero-buttons .btn {
    margin: 0.5rem;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.hero-image {
    position: relative;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.floating-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    color: white;
    animation: float 3s ease-in-out infinite;
}

.floating-card i {
    font-size: 4rem;
    color: #ffd700;
    margin-bottom: 1rem;
}

.floating-card h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.scroll-indicator a {
    color: white;
    font-size: 2rem;
    animation: bounce 2s infinite;
    text-decoration: none;
}

/* Section Styles */
.section-title {
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0 auto 2rem;
    border-radius: 2px;
}

.section-description {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about-section {
    background: white;
}

.about-content h3 {
    font-size: 2.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.about-content .lead {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 2rem;
}

.about-features {
    margin-top: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateX(-10px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.feature-item i {
    font-size: 2rem;
    margin-left: 1rem;
    width: 60px;
    text-align: center;
}

.feature-item h5 {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.feature-item p {
    color: #666;
    margin: 0;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    padding: 2rem;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.stat-number {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* Services Section */
.services-section {
    background: #f8f9fa;
}

.service-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1);
}

.service-icon i {
    font-size: 2rem;
    color: white;
}

.service-card h4 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.service-card p {
    color: #666;
    margin-bottom: 1.5rem;
}

.service-features {
    list-style: none;
    padding: 0;
    text-align: right;
}

.service-features li {
    padding: 0.5rem 0;
    color: #666;
    border-bottom: 1px solid #eee;
}

.service-features li:before {
    content: "✓";
    color: #28a745;
    font-weight: bold;
    margin-left: 0.5rem;
}

/* Portfolio Section */
.portfolio-section {
    background: white;
}

.portfolio-item {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.2);
}

.portfolio-image {
    position: relative;
    overflow: hidden;
}

.portfolio-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.1);
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(102, 126, 234, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-info {
    text-align: center;
    color: white;
}

.portfolio-info h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.portfolio-info p {
    font-size: 1rem;
    opacity: 0.9;
}

/* Contact Section */
.contact-section {
    background: #f8f9fa;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.contact-form h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.contact-form .form-control {
    border: 2px solid #eee;
    border-radius: 10px;
    padding: 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.contact-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.contact-info {
    padding: 2rem;
}

.contact-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 1rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateX(-10px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.contact-item i {
    font-size: 1.5rem;
    color: #667eea;
    margin-left: 1rem;
    margin-top: 0.25rem;
    width: 30px;
    text-align: center;
}

.contact-item h5 {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.contact-item p {
    color: #666;
    margin: 0;
    line-height: 1.6;
}

.social-links {
    margin-top: 2rem;
}

.social-links h5 {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.social-link {
    display: inline-block;
    width: 50px;
    height: 50px;
    background: #667eea;
    color: white;
    text-align: center;
    line-height: 50px;
    border-radius: 50%;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-link:hover {
    transform: translateY(-5px);
    color: white;
    text-decoration: none;
}

.social-link.facebook:hover {
    background: #3b5998;
}

.social-link.instagram:hover {
    background: #e4405f;
}

.social-link.whatsapp:hover {
    background: #25d366;
}

.social-link.youtube:hover {
    background: #ff0000;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.cta-content {
    text-align: center;
    padding: 2rem 0;
}

.cta-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons .btn {
    margin: 0.5rem;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-decoration: none;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Smooth Scroll */
.smooth-scroll {
    scroll-behavior: smooth;
}

/* Button Styles */
.btn {
    border-radius: 50px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

.btn-outline-light {
    border: 2px solid white;
    color: white;
    background: transparent;
}

.btn-outline-light:hover {
    background: white;
    color: #667eea;
}

.btn-success {
    background: #25d366;
    border: none;
    color: white;
}

.btn-light {
    background: white;
    color: #667eea;
    border: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.8rem;
    }

    .hero-description {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }

    .stat-number {
        font-size: 2rem;
    }

    .service-card {
        margin-bottom: 2rem;
    }

    .contact-form,
    .contact-info {
        padding: 1.5rem;
    }

    .hero-buttons .btn {
        display: block;
        margin: 0.5rem 0;
        width: 100%;
    }

    .cta-buttons .btn {
        display: block;
        margin: 0.5rem 0;
        width: 100%;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.5rem;
    }

    .floating-card {
        padding: 1rem;
    }

    .floating-card i {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .about-content h3 {
        font-size: 1.8rem;
    }
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

.mb-5 {
    margin-bottom: 3rem;
}

.py-5 {
    padding-top: 3rem;
    padding-bottom: 3rem;
}

.min-vh-100 {
    min-height: 100vh;
}

.align-items-center {
    align-items: center;
}

/* AOS Animation Support */
[data-aos] {
    opacity: 0;
    transition-property: opacity, transform;
}

[data-aos].aos-animate {
    opacity: 1;
}

[data-aos="fade-up"] {
    transform: translateY(30px);
}

[data-aos="fade-up"].aos-animate {
    transform: translateY(0);
}

[data-aos="fade-right"] {
    transform: translateX(-30px);
}

[data-aos="fade-right"].aos-animate {
    transform: translateX(0);
}

[data-aos="fade-left"] {
    transform: translateX(30px);
}

[data-aos="fade-left"].aos-animate {
    transform: translateX(0);
}

[data-aos="zoom-in"] {
    transform: scale(0.8);
}

[data-aos="zoom-in"].aos-animate {
    transform: scale(1);
}
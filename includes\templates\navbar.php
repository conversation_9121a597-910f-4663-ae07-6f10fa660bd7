    <!-- navbar  -->
    <nav class="navbar navbar-default navbar-fixed-top">
    <div class="container">
    <!-- Brand and toggle get grouped for better mobile display -->
     <div class="navbar-header">
      <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#app-nav" aria-expanded="false">
        <span class="sr-only">Toggle navigation</span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
      </button>
      <a class="navbar-brand" href="index.php">
        <i class="fa fa-snowflake-o"></i> شركة صبري يوسف
      </a>
     </div>
    <div class="collapse navbar-collapse" id="app-nav">
      <ul class="nav navbar-nav">
        <li><a href="index.php"><i class="fa fa-home"></i> الرئيسية</a></li>
        <li><a href="#about" class="smooth-scroll"><i class="fa fa-info-circle"></i> من نحن</a></li>
        <li><a href="#services" class="smooth-scroll"><i class="fa fa-cogs"></i> خدماتنا</a></li>
        <li><a href="#portfolio" class="smooth-scroll"><i class="fa fa-briefcase"></i> أعمالنا</a></li>
        <li><a href="#contact" class="smooth-scroll"><i class="fa fa-phone"></i> اتصل بنا</a></li>
        <?php if (isset($_COOKIE['user'])): ?>
        <li class="dropdown">
          <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false">
            <i class="fa fa-cog"></i> النظام <span class="caret"></span>
          </a>
          <ul class="dropdown-menu">
            <li><a href="dashboard.php"><i class="fa fa-dashboard"></i> لوحة التحكم</a></li>
            <li><a href="Category.php"><i class="fa fa-snowflake-o"></i> المكيفات</a></li>
            <li><a href="maintenance.php"><i class="fa fa-wrench"></i> الصيانة</a></li>
            <li><a href="members.php"><i class="fa fa-users"></i> المستخدمين</a></li>
          </ul>
        </li>
        <?php endif; ?>
      </ul>

      <ul class="nav navbar-nav navbar-right">
        <?php if (isset($_COOKIE['user'])): ?>
        <li><a href="export.php"><i class="fa fa-download"></i> تصدير Excel</a></li>
        <li><a href="logout.php"><i class="fa fa-sign-out"></i> تسجيل الخروج</a></li>
        <?php else: ?>
        <li><a href="login.php"><i class="fa fa-sign-in"></i> تسجيل الدخول</a></li>
        <?php endif; ?>
      </ul>

      <?php if (isset($_COOKIE['user'])): ?>
      <form class="navbar-form navbar-right" action="search.php" method="GET">
        <div class="form-group">
          <input class="form-control" id="query" type="text" name="search_query" placeholder="البحث...">
        </div>
        <button class="btn btn-success" type="submit">
          <i class="fa fa-search"></i>
        </button>
      </form>
      <?php endif; ?>
    </div>
  </div>
</nav>

<!-- Add padding to body to account for fixed navbar -->
<style>
body {
    padding-top: 70px;
}
</style>

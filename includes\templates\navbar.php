    <!-- navbar  -->
    <nav class="navbar navbar-default navbar-fixed-top" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
    <div class="container">
    <!-- Brand and toggle get grouped for better mobile display -->
     <div class="navbar-header">
      <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#app-nav" aria-expanded="false" style="border-color: rgba(255,255,255,0.3); float: left;">
        <span class="sr-only">Toggle navigation</span>
        <span class="icon-bar" style="background-color: white;"></span>
        <span class="icon-bar" style="background-color: white;"></span>
        <span class="icon-bar" style="background-color: white;"></span>
      </button>
      <a class="navbar-brand" href="index.php" style="color: white; font-weight: bold; font-size: 20px; float: right;">
        <i class="fa fa-snowflake-o" style="color: #ffd700;"></i> شركة صبري يوسف
      </a>
     </div>
    <div class="collapse navbar-collapse" id="app-nav">
      <ul class="nav navbar-nav" style="float: right;">
        <li><a href="index.php" style="color: white;"><i class="fa fa-home"></i> الرئيسية</a></li>
        <li><a href="#about" class="smooth-scroll" style="color: white;"><i class="fa fa-info-circle"></i> من نحن</a></li>
        <li><a href="#services" class="smooth-scroll" style="color: white;"><i class="fa fa-cogs"></i> خدماتنا</a></li>
        <li><a href="#portfolio" class="smooth-scroll" style="color: white;"><i class="fa fa-briefcase"></i> أعمالنا</a></li>
        <li><a href="#contact" class="smooth-scroll" style="color: white;"><i class="fa fa-phone"></i> اتصل بنا</a></li>
        <?php if (isset($_COOKIE['user'])): ?>
        <li class="dropdown">
          <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false" style="color: white;">
            <i class="fa fa-cog"></i> النظام <span class="caret"></span>
          </a>
          <ul class="dropdown-menu" style="right: 0; left: auto; text-align: right;">
            <li><a href="dashboard.php" style="text-align: right;"><i class="fa fa-dashboard"></i> لوحة التحكم</a></li>
            <li><a href="Category.php" style="text-align: right;"><i class="fa fa-snowflake-o"></i> المكيفات</a></li>
            <li><a href="maintenance.php" style="text-align: right;"><i class="fa fa-wrench"></i> الصيانة</a></li>
            <li><a href="members.php" style="text-align: right;"><i class="fa fa-users"></i> المستخدمين</a></li>
            <li role="separator" class="divider"></li>
            <li><a href="export.php" style="text-align: right;"><i class="fa fa-download"></i> تصدير Excel</a></li>
          </ul>
        </li>
        <?php endif; ?>
      </ul>

      <ul class="nav navbar-nav" style="float: left;">
        <?php if (isset($_COOKIE['user'])): ?>
        <li class="dropdown">
          <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false" style="color: white;">
            <i class="fa fa-user"></i> <?php echo $_COOKIE['user']; ?> <span class="caret"></span>
          </a>
          <ul class="dropdown-menu" style="right: 0; left: auto; text-align: right;">
            <li><a href="profile.php" style="text-align: right;"><i class="fa fa-user"></i> الملف الشخصي</a></li>
            <li><a href="logout.php" style="text-align: right;"><i class="fa fa-sign-out"></i> تسجيل الخروج</a></li>
          </ul>
        </li>
        <?php else: ?>
        <li><a href="login.php" style="color: white; background: rgba(255,255,255,0.1); border-radius: 20px; margin: 8px;"><i class="fa fa-sign-in"></i> دخول النظام</a></li>
        <?php endif; ?>
      </ul>

      <?php if (isset($_COOKIE['user'])): ?>
      <form class="navbar-form" style="float: left; margin-left: 15px;" action="search.php" method="GET">
        <div class="form-group">
          <input class="form-control" id="query" type="text" name="search_query" placeholder="البحث..." style="border-radius: 20px; text-align: right;">
        </div>
        <button class="btn btn-success" type="submit" style="border-radius: 20px; margin-right: 5px;">
          <i class="fa fa-search"></i>
        </button>
      </form>
      <?php endif; ?>
    </div>
  </div>
</nav>

<style>
/* Navbar RTL Styles */
body {
    padding-top: 70px;
}

.navbar-nav > li > a:hover,
.navbar-nav > li > a:focus {
    color: #ffd700 !important;
    background-color: rgba(255,255,255,0.1) !important;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    color: #ffd700 !important;
}

.dropdown-menu {
    background-color: white;
    border: 1px solid rgba(0,0,0,0.15);
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0,0,0,0.175);
}

.dropdown-menu > li > a:hover {
    background-color: #f5f5f5;
    color: #333;
}

/* Smooth scroll for navbar links */
.smooth-scroll {
    transition: all 0.3s ease;
}

/* Mobile responsive */
@media (max-width: 767px) {
    .navbar-nav {
        margin: 0;
        float: none !important;
    }

    .navbar-nav > li {
        float: none;
    }

    .navbar-nav > li > a {
        text-align: right;
        padding-right: 15px;
    }

    .navbar-form {
        margin: 10px 0;
        float: none !important;
    }

    .dropdown-menu {
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none;
    }

    .dropdown-menu > li > a {
        padding-right: 30px;
        color: white;
    }

    .dropdown-menu > li > a:hover {
        background-color: rgba(255,255,255,0.1);
        color: white;
    }
}
</style>

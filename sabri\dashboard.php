<?php
	ob_start(); // Output Buffering Start

	session_start();

	$PageTitle = 'Dashboard';

	if (isset($_COOKIE['username'])) {
		//echo 'welcome'.$_SESSION['username'];
		include 'init.php';

		$numUsers = 6; // Number Of Latest Users

		$latestUsers = getLatest("*", "users", "userID", $numUsers);

		$numacs = 6; // Number Of Latest ac

		$latestacs = getLatest("*", 'ac', 'Id', $numacs); // Latest Items Array


		?>

		<div class="container home-stats text-center">
			<h1>Dashboard</h1>
			<div class="row">
				<div class="col-md-3">
					<div class="stat st-comments">
							<i class="fa fa-institution"></i>
							<div class="info">
								Total Company
								<span>
									<a href="company.php"><?php echo countItems('Id', 'company') ?></a>
								</span>
							</div>
						</div>
					</div>
				<div class="col-md-3">
					<div class="stat st-pending">
							<i class="fa fa-server"></i>
							<div class="info">
								Total Categories
								<span>
									<a href="categories.php">
										<?php echo countItems("Id", "categories") ?>
									</a>
								</span>
							</div>
						</div>
					</div>
					<div class="col-md-3">
						<div class="stat st-items">
							<i class="fa fa-snowflake-o"></i>
							<div class="info">
								Total Ac
								<span>
									<a href="AC.php"><?php echo countItems('Id', 'ac') ?></a>
								</span>
							</div>
						</div>
					</div>
					<div class="col-md-3">
						<div class="stat st-members">
							<i class="fa fa-users"></i>
							<div class="info">
								Total Members
								<span>
									<a href="members.php"><?php echo countItems('UserID', 'users') ?></a>
								</span>
							</div>
						</div>
					</div>
			</div>
		</div>

		<div class="latest">
			<div class="container">
				<div class="row">
					<div class="col-sm-6">
						<div class="panel panel-default">
							<div class="panel-heading">
								<i class="fa fa-users"></i>
								Latest <?php echo $numUsers ?> Registerd Users 
								<span class="toggle-info pull-right">
											<i class="fa fa-plus fa-lg"></i>
								</span>
							</div>
							<div class="panel-body">
								<ul class="list-unstyled latest-users">
									<?php
									if (! empty($latestUsers)) {
										foreach ($latestUsers as $user) {
														
											echo  '<li>';
												echo $user['username'];
												echo '<a href="members.php?do=Edit&userid=' . $user['userID'] . '">';
													echo '<span class="btn btn-success pull-right">';
														echo '<i class="fa fa-edit"></i> Edit</a>';
														if ( $user['regstatus']  == 0) {
															echo "<a href='members.php?do=Activate&userid=".$user['userID']."' class='btn btn-info pull-right activare'><i class='fa fa-check'> </i> Activare</a>";
														}												
													echo '</span>';
												echo '</a>';
											echo '</li>';
										}
									}else {
										echo 'There\'s No Members To Show';
									}	
									?>
								</ul>					
							</div>
						</div>
					</div>
					<div class="col-sm-6">
						<div class="panel panel-default">
							<div class="panel-heading">
								<i class="fa fa-tag"></i>
								Latest <?php echo $numacs ?> ACs
								<span class="toggle-info pull-right">
											<i class="fa fa-plus fa-lg"></i>
								</span>
							</div>
							<div class="panel-body">
								<ul class="list-unstyled latest-users">
									<?php
									if (! empty($latestacs)) {
										foreach ($latestacs as $item) {			
											echo  '<li>';
												echo $item['Name'];
												echo '<a href="Ac.php?do=Edit&acid=' . $item['Id'] . '">';
													echo '<span class="btn btn-success pull-right">';
														echo '<i class="fa fa-edit"></i> Edit</a>';											
													echo '</span>';
												echo '</a>';
											echo '</li>';
										}
									}else {
											echo 'There\'s No Items To Show';
										}	
									?>
								</ul>			
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

<?php	/*end dashboard page */
		include $tp1 . 'footer.php';
		}
	

	else {

		 header('location: index.php');
		 exit();
	}

	ob_end_flush(); // Release The Output

	?>
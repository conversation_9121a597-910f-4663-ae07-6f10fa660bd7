# نظام إدارة صيانة المكيفات - UNHCR AC Maintenance System

نظام شامل لإدارة صيانة أجهزة التكييف في مكاتب المفوضية السامية للأمم المتحدة لشؤون اللاجئين.

## 🌟 المميزات

- **إدارة المستخدمين**: نظام تسجيل دخول آمن مع صلاحيات متعددة
- **إدارة أجهزة التكييف**: تسجيل وتتبع جميع أجهزة التكييف
- **جدولة الصيانة**: تخطيط وتتبع عمليات الصيانة الدورية والطارئة
- **التقارير والإحصائيات**: تقارير شاملة عن حالة الأجهزة والصيانة
- **دعم متعدد اللغات**: واجهة باللغة العربية والإنجليزية
- **تصميم متجاوب**: يعمل على جميع الأجهزة (كمبيوتر، تابلت، موبايل)

## 🚀 التثبيت والإعداد

### المتطلبات الأساسية
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)
- مكتبة PDO لـ PHP

### خطوات التثبيت

1. **تحميل الملفات**
   ```bash
   git clone https://github.com/your-repo/unhcr-ac-system.git
   cd unhcr-ac-system
   ```

2. **إعداد قاعدة البيانات**
   - أنشئ قاعدة بيانات جديدة باسم `sabricom`
   - استورد ملف `database_setup.sql`
   ```sql
   mysql -u root -p sabricom < database_setup.sql
   ```

3. **إعداد ملف البيئة**
   - انسخ ملف `.env.example` إلى `.env`
   - عدّل إعدادات قاعدة البيانات:
   ```env
   DB_HOST=localhost
   DB_NAME=sabricom
   DB_USER=root
   DB_PASS=your_password
   ```

4. **إعداد الصلاحيات**
   ```bash
   chmod 755 uploads/
   chmod 755 logs/
   chmod 644 .env
   ```

5. **تشغيل النظام**
   - ضع الملفات في مجلد الخادم (htdocs/www)
   - افتح المتصفح واذهب إلى `http://localhost/your-folder`

## 🔧 الإعداد السريع

يمكنك استخدام أدوات الإعداد المدمجة:

1. **صفحة الإعداد**: `setup.php` - لإنشاء المجلدات والملفات المطلوبة
2. **صفحة التشخيص**: `diagnostic.php` - لفحص حالة النظام والملفات

## 📖 كيفية الاستخدام

### تسجيل الدخول الأول
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin`
- يُنصح بتغيير كلمة المرور فور تسجيل الدخول

### الصفحات الرئيسية
- **الصفحة الرئيسية** (`index.php`): صفحة الترحيب والمعلومات العامة
- **تسجيل الدخول** (`login.php`): صفحة تسجيل الدخول والتسجيل
- **لوحة التحكم** (`dashboard.php`): الصفحة الرئيسية للمستخدمين المسجلين

### إدارة أجهزة التكييف
1. اذهب إلى قسم "المكيفات"
2. أضف جهاز جديد بالمعلومات المطلوبة
3. حدد الموقع والفئة والحالة
4. احفظ البيانات

### جدولة الصيانة
1. اختر الجهاز المراد صيانته
2. حدد نوع الصيانة (دورية/طارئة/إصلاح)
3. اختر التاريخ والفني المسؤول
4. أضف ملاحظات إضافية

## 🗂️ هيكل المشروع

```
sabri/
├── index.php              # الصفحة الرئيسية
├── login.php              # صفحة تسجيل الدخول
├── dashboard.php          # لوحة التحكم
├── diagnostic.php         # صفحة التشخيص
├── setup.php              # صفحة الإعداد
├── init.php               # ملف التهيئة الرئيسي
├── .env                   # إعدادات البيئة
├── database_setup.sql     # هيكل قاعدة البيانات
├── USER_GUIDE.md          # دليل المستخدم
├── includes/              # الملفات المساعدة
│   ├── functions/         # الدوال المساعدة
│   └── templates/         # قوالب الصفحات
├── layout/                # ملفات التصميم
│   ├── css/               # ملفات الأنماط
│   └── js/                # ملفات JavaScript
├── sabri/                 # ملفات النظام الأساسية
│   ├── connect.php        # الاتصال بقاعدة البيانات
│   └── functions.php      # الدوال الأساسية
├── uploads/               # ملفات المستخدمين
├── logs/                  # ملفات السجلات
└── assets/                # الموارد الثابتة
```

## 🔒 الأمان

- **تشفير كلمات المرور**: باستخدام SHA1 (يُنصح بالترقية إلى bcrypt)
- **حماية من SQL Injection**: استخدام PDO Prepared Statements
- **حماية الملفات الحساسة**: ملف .htaccess لمنع الوصول المباشر
- **التحقق من الصلاحيات**: فحص صلاحيات المستخدم في كل صفحة

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **ملفات CSS/JS لا تعمل**
   - تحقق من مسارات الملفات في `init.php`
   - تأكد من وجود الملفات في المجلدات الصحيحة
   - فحص صلاحيات الملفات

2. **خطأ في الاتصال بقاعدة البيانات**
   - تحقق من إعدادات `.env`
   - تأكد من تشغيل خادم MySQL
   - تحقق من صحة اسم قاعدة البيانات

3. **صفحة بيضاء أو أخطاء PHP**
   - فعّل عرض الأخطاء في PHP
   - تحقق من ملفات السجلات
   - تأكد من إصدار PHP المطلوب

### أدوات التشخيص
- استخدم `diagnostic.php` لفحص حالة النظام
- راجع ملفات السجلات في مجلد `logs/`
- استخدم أدوات المطور في المتصفح

## 📊 قاعدة البيانات

### الجداول الرئيسية
- **users**: بيانات المستخدمين
- **ac**: معلومات أجهزة التكييف
- **categories**: فئات الأجهزة
- **maintenance**: سجلات الصيانة
- **company**: بيانات الشركة

### العلاقات
- كل جهاز تكييف مرتبط بفئة ومستخدم
- كل سجل صيانة مرتبط بجهاز تكييف ومستخدم

## 🔄 التحديثات والصيانة

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u root -p sabricom > backup_$(date +%Y%m%d).sql

# نسخ احتياطي للملفات
tar -czf files_backup_$(date +%Y%m%d).tar.gz uploads/ logs/
```

### التحديثات
- راجع ملف `CHANGELOG.md` للتحديثات الجديدة
- اعمل نسخة احتياطية قبل أي تحديث
- اتبع تعليمات التحديث في كل إصدار

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير النظام:

1. Fork المشروع
2. أنشئ فرع جديد للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. افتح Pull Request

## 📞 الدعم والتواصل

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +964 770 123 4567
- **الموقع**: [www.sabrihvac.com](http://www.sabrihvac.com)

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- فريق تطوير UNHCR
- مجتمع المطورين العرب
- جميع المساهمين في المشروع

---

**ملاحظة**: هذا النظام تم تطويره خصيصاً للمفوضية السامية للأمم المتحدة لشؤون اللاجئين ويمكن تخصيصه حسب احتياجات المؤسسات الأخرى.

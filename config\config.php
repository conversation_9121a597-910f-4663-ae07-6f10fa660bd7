<?php
/**
 * Configuration File
 * Centralized configuration management for the application
 * 
 * <AUTHOR> HVAC Team
 * @version 2.0
 * @since 2024
 */

// Prevent direct access
if (!defined('APP_INIT')) {
    die('Direct access not permitted');
}

// Load environment variables
function loadEnvironment() {
    $envFile = dirname(__DIR__) . '/.env';
    if (file_exists($envFile)) {
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos(trim($line), '#') === 0) {
                continue; // Skip comments
            }
            
            list($name, $value) = explode('=', $line, 2);
            $name = trim($name);
            $value = trim($value);
            
            if (!array_key_exists($name, $_SERVER) && !array_key_exists($name, $_ENV)) {
                putenv(sprintf('%s=%s', $name, $value));
                $_ENV[$name] = $value;
                $_SERVER[$name] = $value;
            }
        }
    }
}

// Load environment variables
loadEnvironment();

// Application Configuration
return [
    // Application Settings
    'app' => [
        'name' => getenv('APP_NAME') ?: 'شركة صبري للتبريد والتكييف',
        'debug' => filter_var(getenv('APP_DEBUG'), FILTER_VALIDATE_BOOLEAN),
        'timezone' => getenv('APP_TIMEZONE') ?: 'Asia/Baghdad',
        'locale' => 'ar',
        'charset' => 'UTF-8',
        'version' => '2.0.0'
    ],

    // Database Configuration
    'database' => [
        'host' => getenv('DB_HOST') ?: 'localhost',
        'name' => getenv('DB_NAME') ?: 'sabrihvac',
        'user' => getenv('DB_USER') ?: 'root',
        'pass' => getenv('DB_PASS') ?: '',
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci'
        ]
    ],

    // Security Configuration
    'security' => [
        'session_lifetime' => (int)(getenv('SESSION_LIFETIME') ?: 3600),
        'csrf_token_name' => getenv('CSRF_TOKEN_NAME') ?: 'csrf_token',
        'password_min_length' => 8,
        'max_login_attempts' => 5,
        'lockout_duration' => 900, // 15 minutes
        'secure_cookies' => !empty($_SERVER['HTTPS']),
        'same_site_cookies' => 'Strict'
    ],

    // Company Information
    'company' => [
        'name' => 'شركة صبري للتبريد والتكييف',
        'name_en' => 'Sabri HVAC Company',
        'phone' => '+*********** 069',
        'phone_alt' => '+*********** 157',
        'email' => '<EMAIL>',
        'email_support' => '<EMAIL>',
        'address' => 'دمشق - سوريا',
        'address_en' => 'Damascus - syria',
        'website' => 'https://sabrihvac.com',
        'established' => '1992',
        'license' => 'HVAC-1992-BGD-001'
    ],

    // Social Media Links
    'social' => [
       // 'facebook' => 'https://facebook.com/sabrihvac',
       // 'instagram' => 'https://instagram.com/sabrihvac',
        'whatsapp' => 'https://wa.me/963933427069',
       // 'telegram' => 'https://t.me/sabrihvac',
       // 'youtube' => 'https://youtube.com/@sabrihvac'
    ],

    // Email Configuration
    'mail' => [
        'driver' => getenv('MAIL_DRIVER') ?: 'smtp',
        'host' => getenv('MAIL_HOST') ?: 'smtp.gmail.com',
        'port' => (int)(getenv('MAIL_PORT') ?: 587),
        'username' => getenv('MAIL_USERNAME') ?: '',
        'password' => getenv('MAIL_PASSWORD') ?: '',
        'encryption' => getenv('MAIL_ENCRYPTION') ?: 'tls',
        'from_address' => getenv('MAIL_FROM_ADDRESS') ?: '<EMAIL>',
        'from_name' => getenv('MAIL_FROM_NAME') ?: 'شركة صبري للتبريد والتكييف'
    ],

    // File Upload Configuration
    'upload' => [
        'max_size' => 5 * 1024 * 1024, // 5MB
        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
        'upload_path' => 'uploads/',
        'create_thumbnails' => true,
        'thumbnail_sizes' => [
            'small' => [150, 150],
            'medium' => [300, 300],
            'large' => [800, 600]
        ]
    ],

    // Cache Configuration
    'cache' => [
        'driver' => getenv('CACHE_DRIVER') ?: 'file',
        'ttl' => (int)(getenv('CACHE_TTL') ?: 3600),
        'path' => 'cache/',
        'prefix' => 'sabrihvac_'
    ],

    // Logging Configuration
    'logging' => [
        'enabled' => filter_var(getenv('LOG_ENABLED'), FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE) ?? true,
        'level' => getenv('LOG_LEVEL') ?: 'info',
        'path' => 'logs/',
        'max_files' => 30,
        'max_size' => 10 * 1024 * 1024 // 10MB
    ],

    // API Configuration
    'api' => [
        'rate_limit' => 100, // requests per hour
        'version' => 'v1',
        'timeout' => 30,
        'user_agent' => 'SabriHVAC-API/1.0'
    ],

    // Maintenance Configuration
    'maintenance' => [
        'default_interval' => 30, // days
        'reminder_days' => [7, 3, 1], // days before maintenance
        'categories' => [
            'monthly' => 'صيانة شهرية',
            'quarterly' => 'صيانة ربع سنوية',
            'annual' => 'صيانة سنوية',
            'emergency' => 'صيانة طارئة'
        ],
        'priorities' => [
            'low' => 'منخفضة',
            'medium' => 'متوسطة',
            'high' => 'عالية',
            'critical' => 'حرجة'
        ]
    ],

    // Notification Configuration
    'notifications' => [
        'email_enabled' => true,
        'sms_enabled' => false,
        'push_enabled' => false,
        'templates_path' => 'templates/notifications/',
        'queue_enabled' => false
    ],

    // Backup Configuration
    'backup' => [
        'enabled' => true,
        'frequency' => 'daily', // daily, weekly, monthly
        'retention_days' => 30,
        'path' => 'backups/',
        'include_uploads' => true,
        'compress' => true
    ],

    // Performance Configuration
    'performance' => [
        'enable_gzip' => true,
        'enable_caching' => true,
        'minify_html' => !filter_var(getenv('APP_DEBUG'), FILTER_VALIDATE_BOOLEAN),
        'minify_css' => !filter_var(getenv('APP_DEBUG'), FILTER_VALIDATE_BOOLEAN),
        'minify_js' => !filter_var(getenv('APP_DEBUG'), FILTER_VALIDATE_BOOLEAN),
        'lazy_loading' => true
    ],

    // SEO Configuration
    'seo' => [
        'site_name' => 'شركة صبري للتبريد والتكييف',
        'default_title' => 'شركة صبري للتبريد والتكييف - الحلول المتكاملة للتبريد والتكييف',
        'default_description' => 'شركة صبري للتبريد والتكييف - نقدم أفضل خدمات التبريد والتكييف مع فريق من الخبراء المتخصصين وأحدث التقنيات',
        'default_keywords' => 'تكييف, تبريد, صيانة تكييف, تركيب تكييف, HVAC',
        'og_image' => 'assets/images/og-image.jpg',
        'twitter_handle' => '@sabrihvac'
    ],

    // Paths Configuration
    'paths' => [
        'root' => dirname(__DIR__),
        'app' => dirname(__DIR__) . '/app',
        'config' => dirname(__DIR__) . '/config',
        'public' => dirname(__DIR__) . '/public',
        'storage' => dirname(__DIR__) . '/storage',
        'templates' => dirname(__DIR__) . '/templates',
        'assets' => dirname(__DIR__) . '/assets',
        'uploads' => dirname(__DIR__) . '/uploads',
        'logs' => dirname(__DIR__) . '/logs',
        'cache' => dirname(__DIR__) . '/cache',
        'backups' => dirname(__DIR__) . '/backups'
    ],

    // URL Configuration
    'urls' => [
        'base' => rtrim(getenv('APP_URL') ?: 'http://localhost', '/'),
        'assets' => '/assets',
        'uploads' => '/uploads',
        'api' => '/api'
    ],

    // Feature Flags
    'features' => [
        'user_registration' => true,
        'email_verification' => true,
        'two_factor_auth' => false,
        'maintenance_scheduling' => true,
        'inventory_management' => true,
        'reporting' => true,
        'api_access' => false,
        'mobile_app' => false
    ],

    // Third-party Services
    'services' => [
        'google_analytics' => getenv('GOOGLE_ANALYTICS_ID') ?: '',
        'google_maps_api' => getenv('GOOGLE_MAPS_API_KEY') ?: '',
        'recaptcha_site_key' => getenv('RECAPTCHA_SITE_KEY') ?: '',
        'recaptcha_secret_key' => getenv('RECAPTCHA_SECRET_KEY') ?: '',
        'pusher_app_id' => getenv('PUSHER_APP_ID') ?: '',
        'pusher_key' => getenv('PUSHER_KEY') ?: '',
        'pusher_secret' => getenv('PUSHER_SECRET') ?: ''
    ]
];
?>

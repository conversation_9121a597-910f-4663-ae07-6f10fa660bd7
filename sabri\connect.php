<?php
/**
 * Database Connection - Secure Version
 * Enhanced database connection with security improvements
 *
 * <AUTHOR> HVAC Team
 * @version 2.0
 * @since 2024
 */

// Prevent direct access
if (!defined('APP_INIT')) {
    define('APP_INIT', true);
}

// Load environment variables
function loadEnvVars() {
    $envFile = dirname(__DIR__) . '/.env';
    if (file_exists($envFile)) {
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos(trim($line), '#') === 0) continue;

            list($name, $value) = explode('=', $line, 2);
            $name = trim($name);
            $value = trim($value);

            if (!array_key_exists($name, $_ENV)) {
                $_ENV[$name] = $value;
            }
        }
    }
}

loadEnvVars();

// Database configuration with fallback values
$dbConfig = [
    'host' => $_ENV['DB_HOST'] ?? 'localhost',#'sabrihvac.com',
    'name' => $_ENV['DB_NAME'] ?? 'sabricom',#'sabrihva_sabricom',
    'user' => $_ENV['DB_USER'] ?? 'root',
    'pass' => $_ENV['DB_PASS'] ?? '',
    'charset' => 'utf8mb4'
];

// PDO options for security and performance
$options = [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES => false,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$dbConfig['charset']} COLLATE {$dbConfig['charset']}_unicode_ci",
    PDO::ATTR_TIMEOUT => 30,
    PDO::ATTR_PERSISTENT => false
];

try {
    // Create DSN
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['name']};charset={$dbConfig['charset']}";

    // Create PDO connection
    $con = new PDO($dsn, $dbConfig['user'], $dbConfig['pass'], $options);

    // Set timezone
    $con->exec("SET time_zone = '+03:00'");

    // Log successful connection (only in debug mode)
    if (isset($_ENV['APP_DEBUG']) && $_ENV['APP_DEBUG'] === 'true') {
        error_log("Database connection established successfully");
    }

} catch (PDOException $e) {
    // Log error securely
    error_log("Database connection failed: " . $e->getMessage());

    // Show user-friendly error in production
    if (isset($_ENV['APP_DEBUG']) && $_ENV['APP_DEBUG'] === 'true') {
        die("Database connection failed: " . $e->getMessage());
    } else {
        die("عذراً، حدث خطأ في الاتصال بقاعدة البيانات. يرجى المحاولة لاحقاً.");
    }
}

/**
 * Database helper class for secure operations
 */
class DatabaseHelper {
    private $connection;

    public function __construct($connection) {
        $this->connection = $connection;
    }

    /**
     * Execute a prepared statement safely
     */
    public function execute($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database query failed: " . $e->getMessage());
            throw new Exception("Database operation failed");
        }
    }

    /**
     * Get single record
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetch();
    }

    /**
     * Get multiple records
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * Get count of records
     */
    public function count($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetchColumn();
    }

    /**
     * Insert record and return last insert ID
     */
    public function insert($sql, $params = []) {
        $this->execute($sql, $params);
        return $this->connection->lastInsertId();
    }

    /**
     * Update/Delete records and return affected rows
     */
    public function modify($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * Begin transaction
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }

    /**
     * Commit transaction
     */
    public function commit() {
        return $this->connection->commit();
    }

    /**
     * Rollback transaction
     */
    public function rollback() {
        return $this->connection->rollback();
    }
}

// Create database helper instance
$db = new DatabaseHelper($con);

// Clear sensitive variables
unset($dbConfig, $options);
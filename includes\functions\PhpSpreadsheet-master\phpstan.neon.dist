includes:
    - phpstan-baseline.neon
    - vendor/phpstan/phpstan-phpunit/extension.neon
    - vendor/phpstan/phpstan-phpunit/rules.neon

parameters:
    level: 8
    paths:
        - samples/
        - src/
        - tests/
        - infra/
        - bin/generate-document
        - bin/generate-locales
        - bin/check-phpdoc-types
    excludePaths:
        - src/PhpSpreadsheet/Chart/Renderer/JpGraph.php
        - src/PhpSpreadsheet/Chart/Renderer/JpGraphRendererBase.php
        - src/PhpSpreadsheet/Collection/Memory/SimpleCache1.php
        - src/PhpSpreadsheet/Collection/Memory/SimpleCache3.php
        - src/PhpSpreadsheet/Writer/ZipStream2.php
        - src/PhpSpreadsheet/Writer/ZipStream3.php
    parallel:
        processTimeout: 300.0
    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false
    ignoreErrors:
        # Accept a bit anything for assert methods
        - '~^Parameter \#2 .* of static method PHPUnit\\Framework\\Assert\:\:assert\w+\(\) expects .*, .* given\.$~'
        - '~^Variable \$helper might not be defined\.$~'

<?php
include 'init.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

$objPHPExcel = new Spreadsheet();
$sheet = $objPHPExcel->getActiveSheet();

// Set headers
$sheet->setCellValue('A1', 'Code AC');
$sheet->setCellValue('B1', 'Date');
$sheet->setCellValue('C1', 'Type OF Maintenance');

// Add data from MySQL result
$row = 2;

$stmt = $con->prepare("SELECT 
                            maintenance.*, 
                            ac.Name AS category_name
                        FROM 
                            maintenance
                        INNER JOIN 
                            ac 
                        ON 
                            ac.Id = maintenance.ACID 
                        ORDER BY 
                            Id DESC");

// Execute the statement

$stmt->execute();

// Assign to variable 

$allItems = $stmt->fetchAll();

foreach ($allItems as $item) {
    $dateNoe = new DateTime($item['DateNow']);
    $dateNext = new DateTime($item['DateNext']);

    $sheet->setCellValue('A' . $row, $item['category_name']);
    $sheet->setCellValue('B' . $row, $dateNoe->format('Y-m-d'));
    $sheet->setCellValue('c' . $row, $item['Reason']);
    $row++;
}

// Save the Excel file
$filename = 'exported_file.xlsx';
$writer = new Xlsx($objPHPExcel);
$writer->save($filename);
ob_end_clean();

// Set headers for Excel file download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Length: ' . filesize($filename));

// Output the file
readfile($filename);

/*
include 'init.php'; 


use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

$objPHPExcel = new Spreadsheet();
$sheet = $objPHPExcel->getActiveSheet();

// Set headers
$sheet->setCellValue('A1', 'Code AC');
$sheet->setCellValue('B1', 'Date');
$sheet->setCellValue('C1', 'Date Next Maintenance');
$sheet->setCellValue('D1', 'Date');

// Add data from MySQL result
$row = 2;

$stmt = $con->prepare("SELECT 
                            maintenance.*, 
                            ac.Name AS category_name
                        FROM 
                            maintenance
                        INNER JOIN 
                            ac 
                        ON 
                            ac.Id = maintenance.ACID 
                        ORDER BY 
                            Id DESC");

// Execute The Statement

$stmt->execute();

// Assign To Variable 

$allItems = $stmt->fetchAll();

foreach ($allItems as $item) {
    $sheet->setCellValue('A' . $row, $item['category_name']);
    $sheet->setCellValue('B' . $row, $item['DateNow']);
    $sheet->setCellValue('C' . $row, $item['DateNext']);
    $sheet->setCellValue('D' . $row, $item['Reason']);
    $row++;
}

// Save the Excel file
$filename = 'exported_file.xlsx';
$writer = new Xlsx($objPHPExcel);
$writer->save($filename);
ob_end_clean();
// Set headers for Excel file download
header('Content-Description: File Transfer');
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="'.basename($filename).'"');
header('Expires: 0');
header('Cache-Control: must-revalidate');
header('Pragma: public');
header('Content-Length: ' . filesize($filename));
// Output the file
readfile($filename);

?>


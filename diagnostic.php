<?php
/**
 * ملف تشخيص النظام
 * يساعد في فحص مشاكل CSS و JS والإعدادات
 */

// بدء الجلسة
session_start();

// تضمين الإعدادات
include 'init.php';

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص النظام - UNHCR AC System</title>
    <link rel="stylesheet" href="<?php echo $css ?>bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo $css ?>font-awesome.min.css">
    <link rel="stylesheet" href="<?php echo $css ?>front.css">
    <style>
        .diagnostic-card {
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .file-check {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .file-check:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container" style="margin-top: 20px;">
        <div class="row">
            <div class="col-md-12">
                <h1 class="text-center">
                    <i class="fa fa-stethoscope"></i>
                    تشخيص النظام
                </h1>
                <p class="text-center text-muted">فحص حالة النظام والملفات المطلوبة</p>
            </div>
        </div>

        <!-- فحص ملفات CSS -->
        <div class="row">
            <div class="col-md-6">
                <div class="panel panel-default diagnostic-card">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-css3"></i>
                            فحص ملفات CSS
                        </h3>
                    </div>
                    <div class="panel-body">
                        <?php
                        $cssFiles = [
                            'bootstrap.min.css',
                            'font-awesome.min.css',
                            'jquery-ui.css',
                            'jquery.selectBoxIt.css',
                            'front.css'
                        ];

                        foreach ($cssFiles as $file) {
                            $filePath = $css . $file;
                            $fullPath = __DIR__ . '/' . $filePath;
                            $exists = file_exists($fullPath);
                            $readable = $exists ? is_readable($fullPath) : false;
                            $size = $exists ? filesize($fullPath) : 0;
                            
                            echo '<div class="file-check">';
                            echo '<strong>' . $file . '</strong><br>';
                            echo 'المسار: ' . $filePath . '<br>';
                            
                            if ($exists) {
                                echo '<span class="status-ok"><i class="fa fa-check"></i> موجود</span> | ';
                                if ($readable) {
                                    echo '<span class="status-ok"><i class="fa fa-check"></i> قابل للقراءة</span> | ';
                                } else {
                                    echo '<span class="status-error"><i class="fa fa-times"></i> غير قابل للقراءة</span> | ';
                                }
                                echo '<span class="text-info">الحجم: ' . number_format($size / 1024, 2) . ' KB</span>';
                            } else {
                                echo '<span class="status-error"><i class="fa fa-times"></i> غير موجود</span>';
                            }
                            echo '</div>';
                        }
                        ?>
                    </div>
                </div>
            </div>

            <!-- فحص ملفات JS -->
            <div class="col-md-6">
                <div class="panel panel-default diagnostic-card">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-code"></i>
                            فحص ملفات JavaScript
                        </h3>
                    </div>
                    <div class="panel-body">
                        <?php
                        $jsFiles = [
                            'jquery-3.4.1.min.js',
                            'jquery-ui.min.js',
                            'bootstrap.min.js',
                            'jquery.selectBoxIt.min.js',
                            'front.js'
                        ];

                        foreach ($jsFiles as $file) {
                            $filePath = $js . $file;
                            $fullPath = __DIR__ . '/' . $filePath;
                            $exists = file_exists($fullPath);
                            $readable = $exists ? is_readable($fullPath) : false;
                            $size = $exists ? filesize($fullPath) : 0;
                            
                            echo '<div class="file-check">';
                            echo '<strong>' . $file . '</strong><br>';
                            echo 'المسار: ' . $filePath . '<br>';
                            
                            if ($exists) {
                                echo '<span class="status-ok"><i class="fa fa-check"></i> موجود</span> | ';
                                if ($readable) {
                                    echo '<span class="status-ok"><i class="fa fa-check"></i> قابل للقراءة</span> | ';
                                } else {
                                    echo '<span class="status-error"><i class="fa fa-times"></i> غير قابل للقراءة</span> | ';
                                }
                                echo '<span class="text-info">الحجم: ' . number_format($size / 1024, 2) . ' KB</span>';
                            } else {
                                echo '<span class="status-error"><i class="fa fa-times"></i> غير موجود</span>';
                            }
                            echo '</div>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- فحص قاعدة البيانات -->
        <div class="row">
            <div class="col-md-6">
                <div class="panel panel-default diagnostic-card">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-database"></i>
                            فحص قاعدة البيانات
                        </h3>
                    </div>
                    <div class="panel-body">
                        <?php
                        try {
                            // فحص الاتصال بقاعدة البيانات
                            if (isset($con) && $con instanceof PDO) {
                                echo '<div class="file-check">';
                                echo '<span class="status-ok"><i class="fa fa-check"></i> الاتصال بقاعدة البيانات ناجح</span>';
                                echo '</div>';

                                // فحص الجداول المطلوبة
                                $requiredTables = ['users', 'ac', 'categories'];
                                foreach ($requiredTables as $table) {
                                    try {
                                        $stmt = $con->query("SHOW TABLES LIKE '$table'");
                                        $exists = $stmt->rowCount() > 0;
                                        
                                        echo '<div class="file-check">';
                                        echo '<strong>جدول ' . $table . '</strong><br>';
                                        if ($exists) {
                                            echo '<span class="status-ok"><i class="fa fa-check"></i> موجود</span>';
                                            
                                            // عدد السجلات
                                            $countStmt = $con->query("SELECT COUNT(*) FROM $table");
                                            $count = $countStmt->fetchColumn();
                                            echo ' | <span class="text-info">عدد السجلات: ' . $count . '</span>';
                                        } else {
                                            echo '<span class="status-error"><i class="fa fa-times"></i> غير موجود</span>';
                                        }
                                        echo '</div>';
                                    } catch (Exception $e) {
                                        echo '<div class="file-check">';
                                        echo '<strong>جدول ' . $table . '</strong><br>';
                                        echo '<span class="status-error"><i class="fa fa-times"></i> خطأ في الفحص</span>';
                                        echo '</div>';
                                    }
                                }
                            } else {
                                echo '<div class="file-check">';
                                echo '<span class="status-error"><i class="fa fa-times"></i> فشل الاتصال بقاعدة البيانات</span>';
                                echo '</div>';
                            }
                        } catch (Exception $e) {
                            echo '<div class="file-check">';
                            echo '<span class="status-error"><i class="fa fa-times"></i> خطأ في قاعدة البيانات: ' . $e->getMessage() . '</span>';
                            echo '</div>';
                        }
                        ?>
                    </div>
                </div>
            </div>

            <!-- معلومات النظام -->
            <div class="col-md-6">
                <div class="panel panel-default diagnostic-card">
                    <div class="panel-heading">
                        <h3 class="panel-title">
                            <i class="fa fa-info-circle"></i>
                            معلومات النظام
                        </h3>
                    </div>
                    <div class="panel-body">
                        <div class="file-check">
                            <strong>إصدار PHP:</strong> <?php echo phpversion(); ?>
                        </div>
                        <div class="file-check">
                            <strong>خادم الويب:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?>
                        </div>
                        <div class="file-check">
                            <strong>نظام التشغيل:</strong> <?php echo php_uname('s') . ' ' . php_uname('r'); ?>
                        </div>
                        <div class="file-check">
                            <strong>مجلد العمل:</strong> <?php echo __DIR__; ?>
                        </div>
                        <div class="file-check">
                            <strong>المتغيرات:</strong><br>
                            $css = <?php echo $css; ?><br>
                            $js = <?php echo $js; ?><br>
                            $tp1 = <?php echo $tp1; ?>
                        </div>
                        <div class="file-check">
                            <strong>حالة الجلسة:</strong>
                            <?php
                            if (isset($_COOKIE['user'])) {
                                echo '<span class="status-ok">مسجل الدخول (User ID: ' . $_COOKIE['user'] . ')</span>';
                            } else {
                                echo '<span class="status-warning">غير مسجل الدخول</span>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار التنقل -->
        <div class="row">
            <div class="col-md-12 text-center">
                <div class="btn-group" role="group">
                    <a href="index.php" class="btn btn-primary">
                        <i class="fa fa-home"></i> الصفحة الرئيسية
                    </a>
                    <a href="login.php" class="btn btn-success">
                        <i class="fa fa-sign-in"></i> تسجيل الدخول
                    </a>
                    <a href="dashboard.php" class="btn btn-info">
                        <i class="fa fa-dashboard"></i> لوحة التحكم
                    </a>
                    <button onclick="location.reload()" class="btn btn-warning">
                        <i class="fa fa-refresh"></i> إعادة الفحص
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل ملفات JS -->
    <script src="<?php echo $js ?>jquery-3.4.1.min.js"></script>
    <script src="<?php echo $js ?>bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            console.log('تم تحميل jQuery و Bootstrap بنجاح');
            
            // اختبار تحميل CSS
            var testDiv = $('<div>').css('display', 'none').appendTo('body');
            var bootstrapLoaded = testDiv.css('box-sizing') === 'border-box';
            
            if (bootstrapLoaded) {
                console.log('تم تحميل Bootstrap CSS بنجاح');
            } else {
                console.warn('مشكلة في تحميل Bootstrap CSS');
            }
            
            testDiv.remove();
        });
    </script>
</body>
</html>

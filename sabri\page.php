<?php
	
	// هذه الصفحة اذا عندي اكتر من صفحة وعندي صفحة رئيسية بقارن المدخل اذا كان موجود بينقلنا للصفحة تبعو ما كان موجود بينقلنا للصفحة الرئيسية يلي بنحددا 

	/*  conditiom ? true : false
	هذا التابع نفس الشرط الموجود بالكود بالزبط 
		$do = isset($_GET['do']) ? $_GET['do'] : 'Manage';
	*/
	$do = '';

	if (isset($_GET['do'])) {

		$do = $_GET['do'];
		
	} else{

		$do = 'Manage';

	}

	// if the page is main page
	
	if ($do == 'Manage') {
		
		echo 'welcame you are in manage category page';
		echo '<a href="page.php?do=Add"> Add new category +</a>';

	}elseif ($do == 'Add') {
			
		echo 'welcame you are in add category page';

	}elseif ($do == 'Add') {
			
		echo 'welcame you are in add category page';

	}else{

		echo ' error no page';
	}
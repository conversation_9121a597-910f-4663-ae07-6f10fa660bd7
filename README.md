# شركة صبري للتبريد والتكييف - نظام إدارة الصيانة

## 🌟 نظرة عامة

نظام إدارة شامل لشركة صبري للتبريد والتكييف، مصمم لإدارة عمليات الصيانة وتتبع أجهزة التكييف والتبريد بكفاءة عالية.

## ✨ المميزات الرئيسية

- **إدارة أجهزة التكييف**: تسجيل وتتبع جميع أجهزة التكييف
- **نظام الصيانة**: جدولة وتتبع عمليات الصيانة الدورية والطارئة
- **إدارة المستخدمين**: نظام مصادقة آمن مع مستويات صلاحيات مختلفة
- **التقارير**: تقارير شاملة عن حالة الأجهزة وعمليات الصيانة
- **تصدير البيانات**: إمكانية تصدير البيانات إلى Excel
- **واجهة احترافية**: تصميم حديث ومتجاوب مع جميع الأجهزة

## 🛠️ التقنيات المستخدمة

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Framework CSS**: Bootstrap 5.3
- **Icons**: Font Awesome 6.4
- **Charts**: Chart.js
- **Export**: PhpSpreadsheet
- **Animation**: AOS (Animate On Scroll)

## 📋 متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- Composer
- Node.js (للتطوير)

## 🚀 التثبيت

### 1. استنساخ المشروع

```bash
git clone https://github.com/sabrihvac/maintenance-system.git
cd maintenance-system
```

### 2. تثبيت التبعيات

```bash
composer install
```

### 3. إعداد البيئة

```bash
cp .env.example .env
```

قم بتحديث ملف `.env` ببيانات قاعدة البيانات الخاصة بك:

```env
DB_HOST=localhost
DB_NAME=sabrihvac_db
DB_USER=your_username
DB_PASS=your_password
```

### 4. إنشاء قاعدة البيانات

```sql
CREATE DATABASE sabrihvac_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 5. استيراد هيكل قاعدة البيانات

```bash
mysql -u your_username -p sabrihvac_db < database/schema.sql
```

### 6. تعيين الصلاحيات

```bash
chmod 755 uploads/
chmod 755 logs/
chmod 755 cache/
```

## 📁 هيكل المشروع

```
sabrihvac/
├── assets/                 # الأصول (CSS, JS, Images)
│   ├── css/
│   ├── js/
│   └── images/
├── config/                 # ملفات الإعدادات
├── includes/               # الملفات المشتركة
│   ├── functions/
│   └── templates/
├── sabri/                  # لوحة التحكم الإدارية
├── uploads/                # ملفات المستخدمين
├── logs/                   # ملفات السجلات
├── vendor/                 # تبعيات Composer
├── .env                    # متغيرات البيئة
├── .gitignore             # ملفات Git المتجاهلة
├── composer.json          # تبعيات PHP
├── homepage.html          # الصفحة الرئيسية (HTML)
├── homepage.php           # الصفحة الرئيسية (PHP)
├── index.php              # نقطة الدخول الرئيسية
├── login.php              # صفحة تسجيل الدخول
└── README.md              # هذا الملف
```

## 🔐 الأمان

تم تطبيق أفضل ممارسات الأمان:

- **تشفير كلمات المرور**: استخدام `password_hash()` و `password_verify()`
- **حماية CSRF**: رموز CSRF لجميع النماذج
- **SQL Injection Protection**: استخدام Prepared Statements
- **XSS Protection**: تنظيف جميع المدخلات
- **Session Security**: إعدادات جلسة آمنة
- **Environment Variables**: حماية البيانات الحساسة

## 📊 قاعدة البيانات

### الجداول الرئيسية:

- `users`: المستخدمون
- `ac`: أجهزة التكييف
- `maintenance`: سجلات الصيانة
- `categories`: فئات الأجهزة
- `company`: بيانات الشركات

## 🎨 التخصيص

### تخصيص الألوان

يمكنك تخصيص ألوان الموقع من خلال تعديل متغيرات CSS في `assets/css/homepage.css`:

```css
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #1e3d72;
    --accent-color: #00bcd4;
}
```

### إضافة ميزات جديدة

1. أنشئ ملف PHP جديد في المجلد المناسب
2. أضف الروابط في ملف التنقل
3. أنشئ قاعدة البيانات المطلوبة
4. اختبر الميزة الجديدة

## 🧪 الاختبار

```bash
# تشغيل الاختبارات
php tests/run_tests.php

# اختبار الاتصال بقاعدة البيانات
php tests/database_test.php
```

## 📈 الأداء

- **تحسين الصور**: ضغط تلقائي للصور المرفوعة
- **Lazy Loading**: تحميل الصور عند الحاجة
- **Minification**: ضغط ملفات CSS و JS
- **Caching**: نظام تخزين مؤقت للبيانات
- **CDN**: استخدام CDN للمكتبات الخارجية

## 🔧 الصيانة

### النسخ الاحتياطي

```bash
# نسخ احتياطي لقاعدة البيانات
php scripts/backup_database.php

# نسخ احتياطي للملفات
php scripts/backup_files.php
```

### تنظيف السجلات

```bash
# حذف السجلات القديمة
php scripts/cleanup_logs.php
```

## 📞 الدعم الفني

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +964 770 123 4567
- **الموقع**: https://sabrihvac.com

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push إلى الفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📝 سجل التغييرات

### الإصدار 2.0.0 (2024-01-15)
- إعادة تصميم كاملة للواجهة
- تحسينات أمنية شاملة
- إضافة نظام التقارير المتقدم
- تحسين الأداء والسرعة

### الإصدار 1.0.0 (2023-06-01)
- الإصدار الأولي
- إدارة أساسية للصيانة
- نظام المستخدمين البسيط

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين في هذا المشروع:

- فريق التطوير في شركة صبري للتبريد والتكييف
- مجتمع المطورين العرب
- مساهمي المكتبات مفتوحة المصدر المستخدمة

---

**تم تطويره بـ ❤️ من قبل فريق شركة صبري للتبريد والتكييف**

/* ===== GLOBAL STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

/* ===== CUSTOM PROPERTIES ===== */
:root {
    --primary-color: #2c5aa0;
    --secondary-color: #1e3d72;
    --accent-color: #00bcd4;
    --light-blue: #e3f2fd;
    --dark-color: #1a1a1a;
    --light-color: #f8f9fa;
    --white: #ffffff;
    --gradient-primary: linear-gradient(135deg, #2c5aa0 0%, #1e3d72 100%);
    --gradient-accent: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-medium: 0 5px 25px rgba(0,0,0,0.15);
    --shadow-heavy: 0 10px 40px rgba(0,0,0,0.2);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

/* ===== NAVIGATION ===== */
.navbar {
    background: rgba(44, 90, 160, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.navbar.scrolled {
    background: var(--primary-color);
    padding: 0.5rem 0;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--white) !important;
    text-decoration: none;
}

.brand-text {
    background: linear-gradient(45deg, #fff, #00bcd4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-nav .nav-link {
    color: var(--white) !important;
    font-weight: 500;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.navbar-nav .btn {
    background: var(--gradient-accent);
    border: none;
    border-radius: 25px;
    padding: 0.5rem 1.5rem !important;
    font-weight: 600;
}

/* ===== HERO SECTION ===== */
.hero-section {
    min-height: 100vh;
    background: var(--gradient-primary);
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse"><path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
}

.hero-content {
    position: relative;
    z-index: 2;
    color: var(--white);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    background: linear-gradient(45deg, #fff, #00bcd4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.8;
}

.hero-buttons .btn {
    border-radius: 50px;
    padding: 1rem 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    transition: var(--transition);
}

.hero-buttons .btn-primary {
    background: var(--gradient-accent);
    border: none;
    box-shadow: var(--shadow-medium);
}

.hero-buttons .btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

.hero-buttons .btn-outline-light:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-3px);
}

.hero-image {
    position: relative;
    z-index: 2;
}

.hero-image img {
    filter: drop-shadow(0 20px 40px rgba(0,0,0,0.3));
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.scroll-down {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.scroll-down a {
    color: var(--white);
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* ===== FEATURES SECTION ===== */
.features-section {
    background: var(--light-color);
    padding: 5rem 0;
}

.feature-card {
    background: var(--white);
    padding: 2.5rem 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: var(--white);
}

.feature-card h5 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

/* ===== SERVICES SECTION ===== */
.services-section {
    padding: 5rem 0;
    background: var(--white);
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-accent);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 3rem;
}

.service-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    height: 100%;
    border: 1px solid #eee;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
    border-color: var(--accent-color);
}

.service-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    color: var(--white);
}

.service-card h4 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.service-features {
    list-style: none;
    padding: 0;
    margin-top: 1.5rem;
}

.service-features li {
    padding: 0.5rem 0;
    color: #666;
    position: relative;
    padding-right: 1.5rem;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    right: 0;
    color: var(--accent-color);
    font-weight: bold;
}

/* ===== ABOUT SECTION ===== */
.about-section {
    padding: 5rem 0;
    background: var(--light-color);
}

.about-image img {
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
}

.about-content .section-title {
    text-align: right;
}

.about-content .section-title::after {
    left: 0;
    transform: none;
}

.stats-row {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: #666;
    font-weight: 500;
}

/* ===== PROJECTS SECTION ===== */
.projects-section {
    padding: 5rem 0;
    background: var(--white);
}

.project-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.project-image {
    position: relative;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(44, 90, 160, 0.9);
    color: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: var(--transition);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-card:hover .project-image img {
    transform: scale(1.1);
}

/* ===== CONTACT SECTION ===== */
.contact-section {
    padding: 5rem 0;
    background: var(--light-color);
}

.contact-info-row {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.contact-info-item {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    text-align: center;
    flex: 1;
    min-width: 250px;
    transition: var(--transition);
}

.contact-info-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: var(--white);
}

.contact-details h5 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.contact-details p {
    color: #666;
    margin-bottom: 0.25rem;
}

/* ===== FOOTER ===== */
.footer {
    background: var(--dark-color);
    color: var(--white);
}

.footer-brand {
    font-size: 1.25rem;
    font-weight: 600;
}

.social-links {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    text-decoration: none;
    transition: var(--transition);
}

.social-link:hover {
    transform: translateY(-3px);
    color: var(--white);
}

.copyright {
    color: #999;
    margin: 0;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .stats-row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .contact-info-row {
        flex-direction: column;
    }
    
    .social-links {
        justify-content: center;
        margin-top: 1rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }
}

/* ===== NOTIFICATIONS ===== */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    z-index: 9999;
    transform: translateX(400px);
    transition: var(--transition);
    max-width: 400px;
    border-left: 4px solid var(--accent-color);
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-left-color: #28a745;
}

.notification-error {
    border-left-color: #dc3545;
}

.notification-warning {
    border-left-color: #ffc107;
}

.notification-content {
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.notification-message {
    color: var(--dark-color);
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #999;
    cursor: pointer;
    padding: 0;
    margin-right: 1rem;
}

.notification-close:hover {
    color: var(--dark-color);
}

/* ===== SKIP LINK ===== */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: 0 0 4px 4px;
    z-index: 10000;
    transition: var(--transition);
}

.skip-link:focus {
    top: 0;
    color: var(--white);
}

/* ===== LOADING STATES ===== */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--accent-color);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ===== LAZY LOADING ===== */
.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy.loaded {
    opacity: 1;
}

/* ===== PRINT STYLES ===== */
@media print {
    .navbar,
    .hero-section,
    .scroll-down,
    .notification {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    .section-title {
        font-size: 18pt;
        margin-bottom: 10pt;
    }
}

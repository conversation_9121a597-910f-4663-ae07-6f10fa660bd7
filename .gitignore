# Environment files
.env
.env.local
.env.production
.env.staging

# Database
*.sql
*.sqlite
*.db

# Logs
logs/
*.log
error_log

# Cache
cache/
*.cache

# Uploads
uploads/
!uploads/.gitkeep

# Backups
backups/
*.backup

# Temporary files
tmp/
temp/
*.tmp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Composer
vendor/
composer.lock

# Node modules
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build files
dist/
build/

# Configuration files with sensitive data
config/database.php
config/mail.php
config/services.php

# Session files
sessions/

# Error logs
php_errors.log
error.log

# Compiled files
*.compiled

# Package files
*.zip
*.tar.gz
*.rar

# Security
*.key
*.pem
*.crt

# Local development
.htaccess.local
robots.txt.local

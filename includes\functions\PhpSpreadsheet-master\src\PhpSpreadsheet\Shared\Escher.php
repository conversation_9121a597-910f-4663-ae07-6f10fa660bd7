<?php

namespace PhpOffice\PhpSpreadsheet\Shared;

class Escher
{
    /**
     * Drawing Group Container.
     *
     * @var ?<PERSON><PERSON>\DggContainer
     */
    private ?<PERSON><PERSON>\DggContainer $dggContainer = null;

    /**
     * Drawing Container.
     *
     * @var ?<PERSON>scher\DgContainer
     */
    private ?<PERSON><PERSON>\DgContainer $dgContainer = null;

    /**
     * Get Drawing Group Container.
     */
    public function getDggContainer(): ?E<PERSON>\DggContainer
    {
        return $this->dggContainer;
    }

    /**
     * Set Drawing Group Container.
     */
    public function setDggContainer(Escher\DggContainer $dggContainer): <PERSON><PERSON>\DggContainer
    {
        return $this->dggContainer = $dggContainer;
    }

    /**
     * Get Drawing Container.
     */
    public function getDgContainer(): ?Escher\DgContainer
    {
        return $this->dgContainer;
    }

    /**
     * Set Drawing Container.
     */
    public function setDgContainer(<PERSON><PERSON>\DgContainer $dgContainer): <PERSON><PERSON>\DgContainer
    {
        return $this->dgContainer = $dgContainer;
    }
}

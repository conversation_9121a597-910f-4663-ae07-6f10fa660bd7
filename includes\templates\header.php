<!DOCTYPE html>
<html lang="ar" dir="rtl">
    <head>
        <meta charset="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title> <?php getTitle() ?></title>
     <!-- Bootstrap RTL -->
     <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@3.4.1/dist/css/bootstrap.min.css">
     <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-rtl@3.4.0/dist/css/bootstrap-rtl.min.css">
     <link rel="stylesheet" href="<?php echo $css ?>font-awesome.min.css">
     <link rel="stylesheet" href="<?php echo $css ?>jquery-ui.css">
     <link rel="stylesheet" href="<?php echo $css ?>jquery.selectBoxIt.css">
     <!-- Google Fonts -->
     <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
     <!-- AOS Animation Library -->
     <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
     <link rel="stylesheet" href="<?php echo $css ?>front.css">
     <style>
        /* RTL Fixes */
        body {
            direction: rtl;
            text-align: right;
            font-family: 'Tajawal', 'Arial', sans-serif;
        }
        .navbar-brand {
            float: right !important;
        }
        .navbar-nav {
            float: right !important;
        }
        .navbar-right {
            float: left !important;
        }
        .navbar-left {
            float: right !important;
        }
        .pull-left {
            float: right !important;
        }
        .pull-right {
            float: left !important;
        }
        .text-left {
            text-align: right !important;
        }
        .text-right {
            text-align: left !important;
        }
        /* Hero Section RTL */
        .hero-content {
            text-align: right;
        }
        .hero-buttons {
            text-align: right;
        }
        .hero-buttons .btn {
            margin-left: 10px;
            margin-right: 0;
        }
        /* Feature Items RTL */
        .feature-item {
            text-align: right;
        }
        .feature-item i {
            margin-right: 1rem;
            margin-left: 0;
        }
        /* Contact Items RTL */
        .contact-item {
            text-align: right;
        }
        .contact-item i {
            margin-right: 1rem;
            margin-left: 0;
        }
        /* Service Features RTL */
        .service-features {
            text-align: right;
            padding-right: 0;
        }
        .service-features li:before {
            margin-right: 0.5rem;
            margin-left: 0;
        }
        /* Social Links RTL */
        .social-links {
            text-align: right;
        }
        .social-link {
            margin-left: 0.5rem;
            margin-right: 0;
        }
        /* Form RTL */
        .form-control {
            text-align: right;
        }
        /* Dropdown RTL */
        .dropdown-menu {
            right: 0;
            left: auto;
        }
     </style>
    </head>
    <body>
 
 
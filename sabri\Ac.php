<?php

	/*
	================================================
	== Items Page
	================================================
	*/

	ob_start(); // Output Buffering Start

	session_start();

	$pageTitle = 'AC';

	if (isset($_COOKIE['username'])) {

		include 'init.php';

		$do = isset($_GET['do']) ? $_GET['do'] : 'Manage';

		if ($do == 'Manage') {

			$stmt = $con->prepare("SELECT 
										ac.*, 
										categories.Name AS category_name
									FROM 
										ac
									INNER JOIN 
										categories 
									ON 
										categories.Id = ac.CatID 
									ORDER BY 
										Id DESC");

			// Execute The Statement

			$stmt->execute();

			// Assign To Variable 

			$items = $stmt->fetchAll();

			if (! empty($items)) {

			?>

			<h1 class="text-center">Manage ACs</h1>
			<div class="container">
				<div class="table-responsive">
					<table class="main-table text-center table table-bordered">
						<tr>
							<td>#ID</td>
							<td>Ac Name</td>
							<td>Category</td>
							<td>Room No.</td>
							<td>CAP</td>
							<td>Type</td>
							<td>SN</td>
							<td>Brand</td>
							<td>Code</td>
							<td>Control</td>
						</tr>
						<?php
							foreach($items as $item) {
								echo "<tr>";
									echo "<td>" . $item['Id'] . "</td>";
									echo "<td>" . $item['Name'] . "</td>";
									echo "<td>" . $item['category_name'] ."</td>";
									echo "<td>" . $item['RoomNum'] . "</td>";
									echo "<td>" . $item['CAP'] ."</td>";
									echo "<td>" . $item['Type'] . "</td>";
									echo "<td>" . $item['SN'] ."</td>";
									echo "<td>" . $item['Brand'] . "</td>";
									echo "<td>" . $item['Code'] . "</td>";
									echo "<td>
									<a href='maintenance.php?acid=" . $item['Id'] . "' class='btn btn-info'><i class='fa fa-gear'></i> Show</a>
										<a href='Ac.php?do=Edit&acid=" . $item['Id'] . "' class='btn btn-success'><i class='fa fa-edit'></i> Edit</a>
										<a href='Ac.php?do=Delete&acid=" . $item['Id'] . "' class='btn btn-danger confirm'><i class='fa fa-close'></i> Delete </a>";
									echo "</td>";
								echo "</tr>";
							}
						?>
						<tr>
					</table>
				</div>
				<div class="col-sm-offset-5 col-sm-10">
				<a href="Ac.php?do=Add" class="btn btn-sm btn-primary">
					<i class="fa fa-plus"></i> New Ac
				</a>
				</div>
			</div>

			<?php } else {

				echo '<div class="container">';
					echo '<div class="nice-message">There\'s No Acs To Show</div>';
					echo '<a href="Ac.php?do=Add" class="btn btn-sm btn-primary">
							<i class="fa fa-plus"></i> New Item
						</a>';
				echo '</div>';

			} ?>

		<?php 


		} elseif ($do == 'Add') {
			?>

			<h1 class="text-center">Add New Ac</h1>
			<div class="container">
				<form class="form-horizontal" action="?do=Insert" method="POST">
					<!-- Start Name Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Name</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="Name" 
								class="form-control" 
								required="required"  
								placeholder="Name of The Ac" />
						</div>
					</div>
					<!-- End Name Field -->
					<!-- Start Categories Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Category</label>
						<div class="col-sm-10 col-md-6">
							<select name="category">
								<option value="0">...</option>
								<?php
									$allCats = getAllFrom("*", "categories", "", "", "Id","ASC");
									foreach ($allCats as $c) {
										echo "<option value='" . $c['Id'] . "'>" . $c['Name'] . "</option>";
									}	
								?>
							</select>
						</div>
					</div>
					<!-- End Categories Field -->
					<!-- Start Room Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Room No.</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="RoomNum" 
								class="form-control" 
								required="required" 
								placeholder="Room No of The Ac" />
						</div>
					</div>
					<!-- End Room Field -->
					<!-- Start CAP Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">CAP</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="CAP" 
								class="form-control" 
								placeholder="CAP of The Ac" />
						</div>
					</div>
					<!-- End CAP Field -->
					<!-- Start Type Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Type</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="Type" 
								class="form-control" 
								placeholder="Type of The Ac" />
						</div>
					</div>
					<!-- End Type Field -->
					<!-- Start SN Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">SN</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="SN" 
								class="form-control" 
								required="required" 
								placeholder="SN of The Ac" />
						</div>
					</div>
					<!-- End SN Field -->
					<!-- Start Brand Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Brand</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="Brand" 
								class="form-control" 
								required="required" 
								placeholder="Brand of The Ac" />
						</div>
					</div>
					<!-- End Brand Field -->
					<!-- Start Model Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Model</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="Model" 
								class="form-control" 
								required="required" 
								placeholder="Model of The Ac" />
						</div>
					</div>
					<!-- End Model Field -->
					<!-- Start Code Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Code</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="Code" 
								class="form-control" 
								required="required" 
								placeholder="Code of The Ac" />
						</div>
					</div>
					<!-- End Code Field -->
					<!-- Start Submit Field -->
					<div class="form-group form-group-lg">
						<div class="col-sm-offset-5 col-sm-10">
							<input type="submit" value="Add Ac" class="btn btn-primary btn-sm" />
						</div>
					</div>
					<!-- End Submit Field -->
				</form>
			</div>

			<?php
	

		} elseif ($do == 'Insert') {
			if ($_SERVER['REQUEST_METHOD'] == 'POST') {

				echo "<h1 class='text-center'>Insert Item</h1>";
				echo "<div class='container'>";

				// Get Variables From The Form

				$name		= $_POST['Name'];
				$cat		= $_POST['category'];
				$Room 		= $_POST['RoomNum'];
				$CAP 		= $_POST['CAP'];
				$Type 	    = $_POST['Type'];
				$SN 	    = $_POST['SN'];
				$Brand    	= $_POST['Brand'];
				$Model 		= $_POST['Model'];
				$Code		= $_POST['Code'];
				// Validate The Form

				$formErrors = array();

				if (empty($name)) {
					$formErrors[] = 'Name Can\'t be <strong>Empty</strong>';
				}
				if (empty($Room)) {
					$formErrors[] = 'Room No. Can\'t be <strong>Empty</strong>';
				}

				if (empty($SN)) {
					$formErrors[] = 'SN Can\'t be <strong>Empty</strong>';
				}
				if (empty($Brand)) {
					$formErrors[] = 'Brand  Can\'t be <strong>Empty</strong>';
				}

				if (empty($Model)) {
					$formErrors[] = 'Model Can\'t be <strong>Empty</strong>';
				}
				if (empty($Code)) {
					$formErrors[] = 'Code Can\'t be <strong>Empty</strong>';
				}

				if ($cat == 0) {
					$formErrors[] = 'You Must Choose the <strong>Category</strong>';
				}

			

				// Loop Into Errors Array And Echo It

				foreach($formErrors as $error) {
					echo '<div class="alert alert-danger">' . $error . '</div>';
				}

				// Check If There's No Error Proceed The Update Operation

				if (empty($formErrors)) {

					// Insert Userinfo In Database

					$stmt = $con->prepare("INSERT INTO 

						ac(Name, CatID, RoomNum, CAP, Type, SN, Brand, Model,Code)

						VALUES(:zname, :zcat, :zroom, :zcap, :zstype, :zsn, :zbrand, :zmodel, :zcode)");

					$stmt->execute(array(

						'zname' 	=> $name,
						'zcat' 	=> $cat,
						'zroom' 	=> $Room,
						'zcap' 	=> $CAP,
						'zstype' 	=> $Type,
						'zsn'		=> $SN,
						'zbrand'	=> $Brand,
						'zmodel'		=> $Model,
						'zcode'		=> $Code	
						
					));
					// Echo Success Message

					$theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Inserted</div>';

					redirectHome($theMsg, 'back');

				}

			} else {

				echo "<div class='container'>";

				$theMsg = '<div class="alert alert-danger">Sorry You Cant Browse This Page Directly</div>';

				redirectHome($theMsg);

				echo "</div>";

			}

			echo "</div>";


		} elseif ($do == 'Edit') {
			// Check If Get Request item Is Numeric & Get Its Integer Value

			$itemid = isset($_GET['acid']) && is_numeric($_GET['acid']) ? intval($_GET['acid']) : 0;

			// Select All Data Depend On This ID

			$stmt = $con->prepare("SELECT * FROM ac WHERE Id = ?");

			// Execute Query

			$stmt->execute(array($itemid));

			// Fetch The Data

			$item = $stmt->fetch();

			// The Row Count

			$count = $stmt->rowCount();

			// If There's Such ID Show The Form

			if ($count > 0) { ?>

				<h1 class="text-center">Edit Ac</h1>
				<div class="container">
					<form class="form-horizontal" action="?do=Update" method="POST">
						<input type="hidden" name="acid" value="<?php echo $itemid ?>" />
						<!-- Start Name Field -->
						<div class="form-group form-group-lg">
							<label class="col-sm-2 control-label">Name</label>
							<div class="col-sm-10 col-md-6">
								<input 
									type="text" 
									name="Name" 
									class="form-control" 
									required="required"  
									placeholder="Name of The Item"
									value="<?php echo $item['Name'] ?>" />
							</div>
						</div>
						<!-- End Name Field -->
						<!-- Start Categories Field -->
						<div class="form-group form-group-lg">
							<label class="col-sm-2 control-label">Category</label>
							<div class="col-sm-10 col-md-6">
								<select name="category">
									<?php
										$allCats = getAllFrom("*", "categories", "", "", "Id");
										foreach ($allCats as $c) {
											echo "<option value='" . $c['Id'] . "'";
											if ($item['Id'] == $c['Id']) { echo ' selected'; }
											echo ">" . $c['Name'] . "</option>";
										}
									?>
								</select>
							</div>
						</div>
						<!-- End Categories Field -->
					<!-- Start Room Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-2 control-label">Room No.</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="RoomNum" 
								class="form-control" 
								required="required" 
								placeholder="Room No of The Ac"
								value="<?php echo $item['RoomNum'] ?>"  />
						</div>
					</div>
					<!-- End Room Field -->
					<!-- Start CAP Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-2 control-label">CAP</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="CAP" 
								class="form-control" 
								placeholder="CAP of The Ac"
								value="<?php echo $item['CAP'] ?>"  />
						</div>
					</div>
					<!-- End CAP Field -->
					<!-- Start Type Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-2 control-label">Type</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="Type" 
								class="form-control" 
								placeholder="Type of The Ac"
								value="<?php echo $item['Type'] ?>"  />
						</div>
					</div>
					<!-- End Type Field -->
					<!-- Start SN Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-2 control-label">SN</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="SN" 
								class="form-control" 
								required="required" 
								placeholder="SN of The Ac"
								value="<?php echo $item['SN'] ?>"  />
						</div>
					</div>
					<!-- End SN Field -->
					<!-- Start Brand Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-2 control-label">Brand</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="Brand" 
								class="form-control" 
								required="required" 
								placeholder="Brand of The Ac"
								value="<?php echo $item['Brand'] ?>"  />
						</div>
					</div>
					<!-- End Brand Field -->
					<!-- Start Model Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-2 control-label">Model</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="Model" 
								class="form-control" 
								required="required" 
								placeholder="Model of The Ac" 
								value="<?php echo $item['Model'] ?>" />
						</div>
					</div>
					<!-- End Model Field -->
					<!-- Start Code Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-2 control-label">Code</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="Code" 
								class="form-control" 
								required="required" 
								placeholder="Code of The Ac"
								value="<?php echo $item['Code'] ?>"  />
						</div>
					</div>
					<!-- End Code Field -->
						<!--start submit field-->
						<div class="form-group form-group-lg">
							<div class="col-sm-offset-2 col-sm-10 ">
								<input type="submit" value="Save" class="btn btn-primary btn-lg" />
							</div>
						</div>
						<!--end submit field-->
					</form>

					
				</div>
				
			<?php

			// If There's No Such ID Show Error Message

			} else {

				echo "<div class='container'>";

				$theMsg = '<div class="alert alert-danger">Theres No Such ID</div>';

				redirectHome($theMsg);

				echo "</div>";

			}			
			

		} elseif ($do == 'Update') {

			echo "<h1 class='text-center'>Update Item</h1>";
			echo "<div class='container'>";

			if ($_SERVER['REQUEST_METHOD'] == 'POST') {

				// Get Variables From The Form

				$id 		= $_POST['acid'];
				$name		= $_POST['Name'];
				$cat		= $_POST['category'];
				$Room 		= $_POST['RoomNum'];
				$CAP 		= $_POST['CAP'];
				$Type 	    = $_POST['Type'];
				$SN 	    = $_POST['SN'];
				$Brand    	= $_POST['Brand'];
				$Model 		= $_POST['Model'];
				$Code		= $_POST['Code'];
				// Validate The Form

				$formErrors = array();

				if (empty($name)) {
					$formErrors[] = 'Name Can\'t be <strong>Empty</strong>';
				}
				if (empty($Room)) {
					$formErrors[] = 'Room No. Can\'t be <strong>Empty</strong>';
				}

				if (empty($SN)) {
					$formErrors[] = 'SN Can\'t be <strong>Empty</strong>';
				}
				if (empty($Brand)) {
					$formErrors[] = 'Brand  Can\'t be <strong>Empty</strong>';
				}

				if (empty($Model)) {
					$formErrors[] = 'Model Can\'t be <strong>Empty</strong>';
				}
				if (empty($Code)) {
					$formErrors[] = 'Code Can\'t be <strong>Empty</strong>';
				}

				if ($cat == 0) {
					$formErrors[] = 'You Must Choose the <strong>Category</strong>';
				}

				

				// Loop Into Errors Array And Echo It

				foreach($formErrors as $error) {
					echo '<div class="alert alert-danger">' . $error . '</div>';
				}

				// Check If There's No Error Proceed The Update Operation

				if (empty($formErrors)) {

					// Update The Database With This Info

					$stmt = $con->prepare("UPDATE 
												ac 
											SET 
												Name = ?,
												CatID = ?, 
												RoomNum = ?, 
												CAP = ?,
												Type = ?,
												SN = ?,
												Brand = ?,
												Model = ?,
												Code = ?
											WHERE 
												Id = ?");

					$stmt->execute(array($name, $cat, $Room, $CAP, $Type, $SN, $Brand, $Model,$Code, $id));
					// Echo Success Message

					$theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Updated</div>';

					redirectHome($theMsg);

				}

			} else {

				$theMsg = '<div class="alert alert-danger">Sorry You Cant Browse This Page Directly</div>';

				redirectHome($theMsg);

			}

			echo "</div>";

			

		} elseif ($do == 'Delete') {
			
			echo "<h1 class='text-center'>Delete Item</h1>";
			echo "<div class='container'>";

				// Check If Get Request Item ID Is Numeric & Get The Integer Value Of It

				$itemid = isset($_GET['acid']) && is_numeric($_GET['acid']) ? intval($_GET['acid']) : 0;

				// Select All Data Depend On This ID

				$check = checkItem('Id', 'ac', $itemid);

				// If There's Such ID Show The Form

				if ($check > 0) {

					$stmt = $con->prepare("DELETE FROM ac WHERE Id = :zid");

					$stmt->bindParam(":zid", $itemid);

					$stmt->execute();

					$theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Deleted</div>';

					redirectHome($theMsg, 'back');

				} else {

					$theMsg = '<div class="alert alert-danger">This ID is Not Exist</div>';

					redirectHome($theMsg);

				}

			echo '</div>';


		} 

		include $tp1 . 'footer.php';
	
	}else {

		 header('location: index.php');

		 exit();
	
	}

	ob_end_flush(); // Release The Output
?>	
<?php

	/*
	================================================
	== Category Page
	================================================
	*/

	ob_start(); 

	session_start();

	$pageTitle = 'Categories';

		include 'init.php';
if (isset($_COOKIE['user'])) {
		$itemid = isset($_GET['catid']) && is_numeric($_GET['catid']) ? intval($_GET['catid']) : 0;
?>
		<div class="container">
		<?php
					$cat = getItem("Name","categories","Id = $itemid");
					?>
			<h1 class="text-center"><?php echo $cat[0] ?></h1>
			<div class="row">
				<?php
					$allItems = getAllFrom("*","ac","where CatID = $itemid","","Id");
					if (! empty($allItems)) {
					foreach ($allItems as $item) {

							echo '<div class="thumbnail item-box">';
								echo '<div class="caption">';
									echo '<h3><a href="maintenance.php?acid='. $item['Id'] .'">' . $item['Code'] .'</a></h3>';
								echo '</div>';
						echo '</div>';
					}
				}
				?>
			</div>
		</div>
			


<?php
} else {
		header('Location: login.php');
		exit();
	}
		include $tp1 . 'footer.php';
	

	ob_end_flush(); // Release The Output
?>	
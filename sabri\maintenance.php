<?php

	/*
	================================================
	== Items Page
	================================================
	*/

	ob_start(); // Output Buffering Start

	session_start();

	$pageTitle = 'AC';

	if (isset($_COOKIE['username'])) {

		include 'init.php';
		$do = isset($_GET['do']) ? $_GET['do'] : 'Manage';
		// Check If Get Request item Is Numeric & Get Its Integer Value
		$itemid = isset($_GET['acid']) && is_numeric($_GET['acid']) ? intval($_GET['acid']) : 0;
		

		if ($do == 'Manage') {

			$stmt = $con->prepare("SELECT 
										maintenance.*, 
										ac.Name AS category_name
									FROM 
										maintenance
									INNER JOIN 
										ac 
									ON 
										ac.Id = maintenance.ACID 
									WHERE
										ACID = ?
									ORDER BY 
										Id DESC");

			// Execute The Statement

			$stmt->execute(array($itemid));

			// Assign To Variable 

			$items = $stmt->fetchAll();

			if (! empty($items)) {

			?>

			<h1 class="text-center">Manage Maintenance</h1>
			<div class="container">
				<div class="table-responsive">
					<table class="main-table text-center table table-bordered">
						<tr>
							<td>#ID</td>
							<td>Ac</td>
							<td>Date Now</td>
							<td>Date Next</td>
							<td>Reason</td>
							<td>Control</td>

						</tr>
						<?php
							foreach($items as $item) {
								echo "<tr>";
									echo "<td>" . $item['Id'] . "</td>";
									echo "<td>" . $item['category_name'] . "</td>";
									echo "<td>" . $item['DateNow'] ."</td>";
									echo "<td>" . $item['DateNext'] . "</td>";
									echo "<td>" . $item['Reason'] ."</td>";
									echo "<td>
										<a href='maintenance.php?do=Edit&mainid=" . $item['Id'] . "&&acid=". $itemid . "' class='btn btn-success'><i class='fa fa-edit'></i> Edit</a>
										<a href='maintenance.php?do=Delete&mainid=" . $item['Id'] . "' class='btn btn-danger confirm'><i class='fa fa-close'></i> Delete </a>";
									echo "</td>";
								echo "</tr>";
							}
						?>
						<tr>
					</table>
				</div>
				<div class="col-sm-offset-5 col-sm-10">
				<a href="maintenance.php?do=Add&&acid=<?php echo $itemid; ?>"  class="btn btn-sm btn-primary">
					<i class="fa fa-plus"></i> New Maintenance
				</a>
				</div>
			</div>

			<?php } else {

				echo '<div class="container">';
					echo '<div class="nice-message">There\'s No Acs To Show</div>';
					echo '<a href="maintenance.php?do=Add&&acid=' . $itemid . '"  class="btn btn-sm btn-primary">
							<i class="fa fa-plus"></i> New Maintenance
						</a>';
				echo '</div>';

			} ?>

		<?php 


		} elseif ($do == 'Add') {
			$mainid = isset($_GET['acid']) && is_numeric($_GET['acid']) ? intval($_GET['acid']) : 0;
			?>

			
			<div class="container">
				<form class="form-horizontal" action="?do=Insert" method="POST">
					<!-- Start AC Field -->
					<div class="form-group form-group-lg">
						<div class="col-sm-10 col-md-12">
							<?php
							$stmt = $con->prepare("SELECT Name FROM ac WHERE Id = ?");

							// Execute The Statement

							$stmt->execute(array($mainid));

							// Assign To Variable 

							$ac = $stmt->fetchColumn();?>
							<input type="hidden" name="ac" value="<?php echo $mainid ?>" />
							<h1 class="text-center" name ="ac"><?php echo $ac; ?></h1>
						</div>
					</div>

					<!-- End AC Field -->
					<!-- Start Date now Field -->
					<?php $currentDateTime = new DateTime('now');
					 $currentDate = $currentDateTime->format('Y-m-d');
					 $add7days =  date('Y-m-d', strtotime('+7 days'));
					// $add = $add7days->format('Y-m-d');
					 ?>
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Date Now</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="Date" 
								name="DateNow" 
								class="form-control" 
								value="<?php echo strftime('%Y-%m-%d',strtotime($currentDate)) ?>" />
						</div>
					</div>
					<!-- End Date now Field -->
					<!-- Start Next Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Date Next</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="Date" 
								name="DateNext" 
								class="form-control"
								value="<?php echo strftime('%Y-%m-%d',strtotime("$currentDate +1 month")) ?>"   />
						</div>
					</div>
					<!-- End Next Field -->
					<!-- Start Reason Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Reason</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="Reason" 
								class="form-control" 
								placeholder="Reason of The Ac"
								value = "Monthly maintenance" />
						</div>
					</div>
					<!-- End Reason Field -->
					<!-- Start Submit Field -->
					<div class="form-group form-group-lg">
						<div class="col-sm-offset-5 col-sm-10">
							<input type="submit" value="Add maintenance" class="btn btn-primary btn-sm" />
						</div>
					</div>
					<!-- End Submit Field -->
				</form>
			</div>

			<?php
	

		} elseif ($do == 'Insert') {
			if ($_SERVER['REQUEST_METHOD'] == 'POST') {

				echo "<h1 class='text-center'>Insert maintenance</h1>";
				echo "<div class='container'>";

				// Get Variables From The Form

				$ac 	    = $_POST['ac'];
				$now 		= $_POST['DateNow'];
				$next 		= $_POST['DateNext'];
				$reason		= $_POST['Reason'];

				// Validate The Form

				$formErrors = array();

				if (empty($now)) {
					$formErrors[] = 'Date Now Can\'t be <strong>Empty</strong>';
				}

				if (empty($next)) {
					$formErrors[] = 'Date Next Can\'t be <strong>Empty</strong>';
				}

				if (empty($ac)) {
					$formErrors[] = 'You Must Choose the <strong>AC</strong>';
				}

			

				// Loop Into Errors Array And Echo It

				foreach($formErrors as $error) {
					echo '<div class="alert alert-danger">' . $error . '</div>';
				}

				// Check If There's No Error Proceed The Update Operation

				if (empty($formErrors)) {

					// Insert Userinfo In Database

					$stmt = $con->prepare("INSERT INTO 

						maintenance(ACID, DateNow, DateNext, Reason)

						VALUES(:zac, :znow, :znext, :zreason)");

					$stmt->execute(array(

						'zac' 	=> $ac,
						'znow' 	=> $now,
						'znext' 	=> $next,
						'zreason' 	=> $reason,
						
					));
					// Echo Success Message

					$theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Inserted</div>';

					redirectHome($theMsg, 'back');

				}

			} else {

				echo "<div class='container'>";

				$theMsg = '<div class="alert alert-danger">Sorry You Cant Browse This Page Directly</div>';

				redirectHome($theMsg);

				echo "</div>";

			}

			echo "</div>";


		} elseif ($do == 'Edit') {
			// Check If Get Request item Is Numeric & Get Its Integer Value

			$itemid = isset($_GET['mainid']) && is_numeric($_GET['mainid']) ? intval($_GET['mainid']) : 0;
			$mainid = isset($_GET['acid']) && is_numeric($_GET['acid']) ? intval($_GET['acid']) : 0;

			// Select All Data Depend On This ID

			$stmt = $con->prepare("SELECT * FROM maintenance WHERE Id = ?");

			// Execute Query

			$stmt->execute(array($itemid));

			// Fetch The Data

			$item = $stmt->fetch();

			// The Row Count

			$count = $stmt->rowCount();

			// If There's Such ID Show The Form

			if ($count > 0) { ?>

				<div class="container">
					<form class="form-horizontal" action="?do=Update" method="POST">
						<input type="hidden" name="mainid" value="<?php echo $itemid ?>" />
						<!-- Start AC Field -->
					<div class="form-group form-group-lg">
						<div class="col-sm-10 col-md-12">
							<?php
							$stmt = $con->prepare("SELECT Name FROM ac WHERE Id = ?");

							// Execute The Statement

							$stmt->execute(array($mainid));

							// Assign To Variable 

							$ac = $stmt->fetchColumn();?>
							<input type="hidden" name="ac" value="<?php echo $mainid ?>" />
							<h1 class="text-center" name ="ac"><?php echo $ac; ?></h1>
						</div>
					</div>

					<!-- End AC Field -->
					<!-- Start Date now Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Date Now</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="Date" 
								name="DateNow" 
								class="form-control" 
								value="<?php  echo strftime('%Y-%d-%m',strtotime($item['DateNow'])) ?>" />
						</div>
					</div>
					<!-- End Date now Field -->
					<!-- Start Next Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Date Next</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="Date" 
								name="DateNext" 
								class="form-control" 
								value="<?php  echo strftime('%Y-%d-%m',strtotime($item['DateNext'])) ?>" />
						</div>
					</div>
					<!-- End Next Field -->
					<!-- Start Reason Field -->
					<div class="form-group form-group-lg">
						<label class="col-sm-3 control-label">Reason</label>
						<div class="col-sm-10 col-md-6">
							<input 
								type="text" 
								name="Reason" 
								class="form-control" 
								placeholder="Reason of The Ac"
								value="<?php echo $item['Reason'] ?> " />
						</div>
					</div>
					<!-- End Reason Field -->
						<!--start submit field-->
						<div class="form-group form-group-lg">
							<div class="col-sm-offset-5 col-sm-10 ">
								<input type="submit" value="Save" class="btn btn-primary btn-lg" />
							</div>
						</div>
						<!--end submit field-->
					</form>

					
				</div>
				
			<?php

			// If There's No Such ID Show Error Message

			} else {

				echo "<div class='container'>";

				$theMsg = '<div class="alert alert-danger">Theres No Such ID</div>';

				redirectHome($theMsg);

				echo "</div>";

			}			
			

		} elseif ($do == 'Update') {

			echo "<h1 class='text-center'>Update Item</h1>";
			echo "<div class='container'>";

			if ($_SERVER['REQUEST_METHOD'] == 'POST') {

				// Get Variables From The Form

				$id 		= $_POST['mainid'];
				$ac 	    = $_POST['ac'];
				$now 		= $_POST['DateNow'];
				$next 		= $_POST['DateNext'];
				$reason		= $_POST['Reason'];

				$formErrors = array();

				if (empty($now)) {
					$formErrors[] = 'Date Now Can\'t be <strong>Empty</strong>';
				}
				if (empty($next)) {
					$formErrors[] = 'Date Next Can\'t be <strong>Empty</strong>';
				}

				if ($ac == 0) {
					$formErrors[] = 'You Must Choose the <strong>Ac</strong>';
				}

				

				// Loop Into Errors Array And Echo It

				foreach($formErrors as $error) {
					echo '<div class="alert alert-danger">' . $error . '</div>';
				}

				// Check If There's No Error Proceed The Update Operation

				if (empty($formErrors)) {

					// Update The Database With This Info

					$stmt = $con->prepare("UPDATE 
												maintenance 
											SET 
												ACID = ?,
												DateNow = ?, 
												DateNext = ?, 
												Reason = ?
											WHERE 
												Id = ?");

					$stmt->execute(array($ac, $now, $next, $reason, $id));
					// Echo Success Message

					$theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Updated</div>';

					redirectHome($theMsg);

				}

			} else {

				$theMsg = '<div class="alert alert-danger">Sorry You Cant Browse This Page Directly</div>';

				redirectHome($theMsg);

			}

			echo "</div>";

			

		} elseif ($do == 'Delete') {
			
			echo "<h1 class='text-center'>Delete Item</h1>";
			echo "<div class='container'>";

				// Check If Get Request Item ID Is Numeric & Get The Integer Value Of It

				$itemid = isset($_GET['mainid']) && is_numeric($_GET['mainid']) ? intval($_GET['mainid']) : 0;

				// Select All Data Depend On This ID

				$check = checkItem('Id', 'maintenance', $itemid);

				// If There's Such ID Show The Form

				if ($check > 0) {

					$stmt = $con->prepare("DELETE FROM maintenance WHERE Id = :zid");

					$stmt->bindParam(":zid", $itemid);

					$stmt->execute();

					$theMsg = "<div class='alert alert-success'>" . $stmt->rowCount() . ' Record Deleted</div>';

					redirectHome($theMsg, 'back');

				} else {

					$theMsg = '<div class="alert alert-danger">This ID is Not Exist</div>';

					redirectHome($theMsg);

				}

			echo '</div>';


		} 

		include $tp1 . 'footer.php';
	
	}else {

		 header('location: index.php');

		 exit();
	
	}

	ob_end_flush(); // Release The Output
?>	
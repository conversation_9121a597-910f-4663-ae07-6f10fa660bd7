<?php
/**
 * Homepage - Sabri HVAC Company
 * Professional homepage for HVAC services company
 * 
 * <AUTHOR> HVAC Team
 * @version 2.0
 * @since 2024
 */

// Start session and output buffering
session_start();
ob_start();

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// Load environment variables (if using .env)
if (file_exists('.env')) {
    $env = parse_ini_file('.env');
    foreach ($env as $key => $value) {
        $_ENV[$key] = $value;
    }
}

// Configuration
$config = [
    'app_name' => $_ENV['APP_NAME'] ?? 'شركة صبري للتبريد والتكييف',
    'app_debug' => $_ENV['APP_DEBUG'] ?? false,
    'company_phone' => '+*********** 069',
    'company_email' => '<EMAIL>',
    'company_address' => 'سوريا - دمشق',
    'social_links' => [
        'facebook' => '#',
        'instagram' => '#',
        'whatsapp' => 'https://wa.me/963933427069'
    ]
];

// CSRF Token generation
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Page metadata
$page_title = $config['app_name'] . ' - الحلول المتكاملة للتبريد والتكييف';
$page_description = 'شركة صبري للتبريد والتكييف - نقدم أفضل خدمات التبريد والتكييف مع فريق من الخبراء المتخصصين وأحدث التقنيات';
$page_keywords = 'تكييف, تبريد, صيانة تكييف, تركيب تكييف, HVAC';

// Handle contact form submission
$form_message = '';
$form_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['contact_form'])) {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $form_message = 'خطأ في الأمان. يرجى المحاولة مرة أخرى.';
        $form_type = 'error';
    } else {
        // Sanitize and validate input
        $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
        $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
        $phone = filter_input(INPUT_POST, 'phone', FILTER_SANITIZE_STRING);
        $message = filter_input(INPUT_POST, 'message', FILTER_SANITIZE_STRING);
        
        $errors = [];
        
        if (empty($name) || strlen($name) < 2) {
            $errors[] = 'الاسم مطلوب ويجب أن يكون أكثر من حرفين';
        }
        
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'البريد الإلكتروني غير صحيح';
        }
        
        if (empty($phone) || strlen($phone) < 10) {
            $errors[] = 'رقم الهاتف مطلوب ويجب أن يكون صحيحاً';
        }
        
        if (empty($message) || strlen($message) < 10) {
            $errors[] = 'الرسالة مطلوبة ويجب أن تكون أكثر من 10 أحرف';
        }
        
        if (empty($errors)) {
            // Here you would typically save to database or send email
            // For now, we'll just show a success message
            $form_message = 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.';
            $form_type = 'success';
            
            // Generate new CSRF token
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        } else {
            $form_message = implode('<br>', $errors);
            $form_type = 'error';
        }
    }
}

// Function to safely output data
function safe_output($data) {
    return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
}

// Function to generate asset URL with version for cache busting
function asset_url($path) {
    $file_path = __DIR__ . '/' . $path;
    $version = file_exists($file_path) ? filemtime($file_path) : time();
    return $path . '?v=' . $version;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo safe_output($page_description); ?>">
    <meta name="keywords" content="<?php echo safe_output($page_keywords); ?>">
    <meta name="author" content="<?php echo safe_output($config['app_name']); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo safe_output($page_title); ?>">
    <meta property="og:description" content="<?php echo safe_output($page_description); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo safe_output($_SERVER['REQUEST_URI']); ?>">
    <meta property="og:image" content="<?php echo asset_url('assets/images/og-image.jpg'); ?>">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo safe_output($page_title); ?>">
    <meta name="twitter:description" content="<?php echo safe_output($page_description); ?>">
    
    <title><?php echo safe_output($page_title); ?></title>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" as="style">
    <link rel="preload" href="<?php echo asset_url('assets/css/homepage.css'); ?>" as="style">
    
    <!-- Stylesheets -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-9ndCyUa6J4lNdGaVfk9Goj2OqNQQgOHdvBcjdlgr9xVrAIZVANVoELVjYjLdMvNZ" crossorigin="anonymous">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo asset_url('assets/css/homepage.css'); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo asset_url('assets/images/favicon.ico'); ?>">
    <link rel="apple-touch-icon" href="<?php echo asset_url('assets/images/apple-touch-icon.png'); ?>">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "name": "<?php echo safe_output($config['app_name']); ?>",
        "description": "<?php echo safe_output($page_description); ?>",
        "telephone": "<?php echo safe_output($config['company_phone']); ?>",
        "email": "<?php echo safe_output($config['company_email']); ?>",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "<?php echo safe_output($config['company_address']); ?>",
            "addressLocality": "دمشق",
            "addressCountry": "سوريا"
        },
        "serviceType": "HVAC Services",
        "areaServed": "دمشق سوريا"
    }
    </script>
</head>
<body>
    <!-- Skip to main content link for accessibility -->
    <a href="#main-content" class="skip-link">تخطي إلى المحتوى الرئيسي</a>
    
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top" role="navigation" aria-label="التنقل الرئيسي">
        <div class="container">
            <a class="navbar-brand" href="#home" aria-label="الصفحة الرئيسية">
                <i class="fas fa-snowflake me-2" aria-hidden="true"></i>
                <span class="brand-text"><?php echo safe_output($config['app_name']); ?></span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="تبديل التنقل">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#home">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#services">خدماتنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#about">من نحن</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#projects">مشاريعنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">اتصل بنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-primary px-3 ms-2" href="login.php">دخول النظام</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
        <!-- Hero Section -->
        <section id="home" class="hero-section" aria-label="القسم الرئيسي">
            <div class="hero-overlay" aria-hidden="true"></div>
            <div class="container">
                <div class="row align-items-center min-vh-100">
                    <div class="col-lg-6" data-aos="fade-right">
                        <div class="hero-content">
                            <h1 class="hero-title">
                                الحلول المتكاملة للتبريد والتكييف
                            </h1>
                            <p class="hero-subtitle">
                                نقدم أفضل خدمات التبريد والتكييف مع فريق من الخبراء المتخصصين
                                وأحدث التقنيات لضمان راحتكم على مدار السنة
                            </p>
                            <div class="hero-buttons">
                                <a href="#services" class="btn btn-primary btn-lg me-3" aria-label="عرض خدماتنا">
                                    <i class="fas fa-tools me-2" aria-hidden="true"></i>
                                    خدماتنا
                                </a>
                                <a href="#contact" class="btn btn-outline-light btn-lg" aria-label="التواصل معنا">
                                    <i class="fas fa-phone me-2" aria-hidden="true"></i>
                                    اتصل بنا
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6" data-aos="fade-left">
                        <div class="hero-image">
                            <img src="<?php echo asset_url('assets/images/hero-ac.png'); ?>" 
                                 alt="أجهزة التكييف الحديثة" 
                                 class="img-fluid"
                                 loading="eager">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Scroll Down Arrow -->
            <div class="scroll-down" aria-hidden="true">
                <a href="#services" aria-label="انتقل إلى قسم الخدمات">
                    <i class="fas fa-chevron-down"></i>
                </a>
            </div>
        </section>

        <!-- Display form messages -->
        <?php if (!empty($form_message)): ?>
        <div class="alert alert-<?php echo $form_type === 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show" role="alert">
            <?php echo $form_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="إغلاق"></button>
        </div>
        <?php endif; ?>

        <!-- Features Section -->
        <section class="features-section py-5" aria-label="مميزاتنا">
            <div class="container">
                <div class="row">
                    <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                        <div class="feature-card text-center">
                            <div class="feature-icon" aria-hidden="true">
                                <i class="fas fa-clock"></i>
                            </div>
                            <h3>خدمة 24/7</h3>
                            <p>نقدم خدماتنا على مدار الساعة لضمان راحتكم</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                        <div class="feature-card text-center">
                            <div class="feature-icon" aria-hidden="true">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <h3>ضمان شامل</h3>
                            <p>ضمان على جميع أعمالنا وقطع الغيار المستخدمة</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="300">
                        <div class="feature-card text-center">
                            <div class="feature-icon" aria-hidden="true">
                                <i class="fas fa-users"></i>
                            </div>
                            <h3>فريق خبير</h3>
                            <p>فنيون مدربون ومعتمدون في جميع أنواع أجهزة التكييف</p>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="400">
                        <div class="feature-card text-center">
                            <div class="feature-icon" aria-hidden="true">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <h3>أسعار تنافسية</h3>
                            <p>أفضل الأسعار في السوق مع جودة عالية</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Continue with other sections... -->
        <!-- Note: Due to length constraints, I'll continue with the rest in the next file -->
        
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" 
            integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz" 
            crossorigin="anonymous"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="<?php echo asset_url('assets/js/homepage.js'); ?>"></script>
    
    <?php if ($config['app_debug']): ?>
    <!-- Debug information (only in development) -->
    <script>
        console.log('Debug mode enabled');
        console.log('PHP Version: <?php echo PHP_VERSION; ?>');
        console.log('Session ID: <?php echo session_id(); ?>');
    </script>
    <?php endif; ?>
</body>
</html>
<?php
ob_end_flush();
?>

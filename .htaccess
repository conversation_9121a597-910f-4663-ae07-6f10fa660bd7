 Options -Indexes
 ErrorDocument 403 "Sorry, can't allow you access to directory"
 
 <FilesMatch "\.(rb|py|js|css|html|txt|ps)$">
    order deny,allow
	deny from all
</FilesMatch>

 <IfModule module_headers.c>
  Header set X-XSS-Protection "1; mode=block"
  Header set X-Frame-Options "DENY"
  Header set Cross-Origin-Embedder-Policy "require-corp"
  Header set Cross-Origin-Opener-Policy "same-origin"
  Header set Cross-Origin-Resource-Policy "same-site | same-origin | cross-origin"
  Header set X-Content-Type-Options "nosniff"
  Header always set Strict-Transport-Security: "max-age=63072000; includeSubDomains; preload"
  Header set Referrer-Policy "no-referrer, strict-origin-when-cross-origin"
  Header set Content-Security-Policy "default-src https:; font-src https: data:; img-src https: data:; script-src https:; style-src https:;"
  Header set Permissions-Policy "geolocation=self"
</IfModule>
